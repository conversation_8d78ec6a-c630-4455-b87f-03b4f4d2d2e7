# Rigaddon Animation - UI Lists
# Created by: <PERSON><PERSON><PERSON>

import bpy
from bpy.types import <PERSON><PERSON><PERSON>, Operator
from bpy.props import String<PERSON>roperty, IntProperty, BoolProperty, EnumProperty

class RIGADDON_UL_AnimationList(UIList):
    """UIList for displaying animations"""
    
    def draw_item(self, context, layout, data, item, icon, active_data, active_propname, index):
        if self.layout_type in {'DEFAULT', 'COMPACT'}:
            # Animation item
            if item:
                # Main row
                row = layout.row(align=True)
                
                # Enabled/disabled icon
                if item.rigaddon_anim_enabled:
                    row.prop(item, "rigaddon_anim_enabled", text="", icon='HIDE_OFF', emboss=False)
                else:
                    row.prop(item, "rigaddon_anim_enabled", text="", icon='HIDE_ON', emboss=False)
                
                # Animation name
                row.prop(item, "rigaddon_anim_name", text="", emboss=False)
                
                # Target object
                if item.rigaddon_anim_target_object:
                    target_obj = bpy.data.objects.get(item.rigaddon_anim_target_object)
                    if target_obj:
                        row.label(text=f"({target_obj.name})", icon='OBJECT_DATA')
                    else:
                        row.label(text="(Missing)", icon='ERROR')
                
                # Loop indicator
                if item.rigaddon_anim_loop:
                    row.label(text="", icon='FILE_REFRESH')
                
                # Play controls
                play_row = row.row(align=True)
                play_row.scale_x = 0.8
                
                # Play button
                play_op = play_row.operator("rigaddon.play_animation", text="", icon='PLAY')
                play_op.rigaddon_animation_index = index
                
                # Loop play button
                loop_op = play_row.operator("rigaddon.play_animation_loop", text="", icon='FILE_REFRESH')
                loop_op.rigaddon_animation_index = index
                
            else:
                layout.label(text="", translate=False, icon_value=icon)
                
        elif self.layout_type == 'GRID':
            layout.alignment = 'CENTER'
            layout.label(text="", icon_value=icon)
    
    def draw_filter(self, context, layout):
        """Draw filter options"""
        row = layout.row()
        
        subrow = row.row(align=True)
        subrow.prop(self, "filter_name", text="")
        subrow.prop(self, "use_filter_invert", text="", icon='ARROW_LEFTRIGHT')
        
        subrow = row.row(align=True)
        subrow.prop(self, "use_filter_sort_alpha", text="", icon='SORTBYEXT')
        subrow.prop(self, "use_filter_sort_reverse", text="", icon='SORT_DESC')
    
    def filter_items(self, context, data, propname):
        """Filter and sort items"""
        animations = getattr(data, propname)
        helper_funcs = bpy.types.UI_UL_list
        
        # Initialize filter and order lists
        flt_flags = []
        flt_neworder = []
        
        # Filter by name
        if self.filter_name:
            flt_flags = helper_funcs.filter_items_by_name(
                self.filter_name, self.bitflag_filter_item, animations, "rigaddon_anim_name",
                reverse=self.use_filter_invert
            )
        
        if not flt_flags:
            flt_flags = [self.bitflag_filter_item] * len(animations)
        
        # Sort alphabetically
        if self.use_filter_sort_alpha:
            flt_neworder = helper_funcs.sort_items_by_name(animations, "rigaddon_anim_name")
            if self.use_filter_sort_reverse:
                flt_neworder.reverse()
        
        return flt_flags, flt_neworder

class RIGADDON_OT_AnimationListActions(Operator):
    """Animation list actions"""
    bl_idname = "rigaddon.animation_list_actions"
    bl_label = "Animation List Actions"
    bl_description = "Perform actions on animation list"
    bl_options = {'REGISTER', 'UNDO'}
    
    rigaddon_action: EnumProperty(
        name="Action",
        description="Action to perform",
        items=[
            ('ADD', 'Add', 'Add new animation'),
            ('REMOVE', 'Remove', 'Remove selected animation'),
            ('DUPLICATE', 'Duplicate', 'Duplicate selected animation'),
            ('MOVE_UP', 'Move Up', 'Move animation up in list'),
            ('MOVE_DOWN', 'Move Down', 'Move animation down in list'),
            ('CLEAR', 'Clear', 'Clear all animations'),
        ],
        default='ADD'
    )
    
    @classmethod
    def poll(cls, context):
        return True
    
    def execute(self, context):
        anim_props = context.scene.rigaddon_animation_props
        
        if self.rigaddon_action == 'ADD':
            return bpy.ops.rigaddon.add_animation('INVOKE_DEFAULT')
            
        elif self.rigaddon_action == 'REMOVE':
            return bpy.ops.rigaddon.remove_animation('INVOKE_DEFAULT')
            
        elif self.rigaddon_action == 'DUPLICATE':
            return bpy.ops.rigaddon.duplicate_animation('INVOKE_DEFAULT')
            
        elif self.rigaddon_action == 'MOVE_UP':
            return self.rigaddon_move_animation(context, -1)
            
        elif self.rigaddon_action == 'MOVE_DOWN':
            return self.rigaddon_move_animation(context, 1)
            
        elif self.rigaddon_action == 'CLEAR':
            return bpy.ops.rigaddon.clear_all_animations('INVOKE_DEFAULT')
        
        return {'FINISHED'}
    
    def rigaddon_move_animation(self, context, direction):
        """Move animation up or down in the list"""
        try:
            anim_props = context.scene.rigaddon_animation_props
            current_index = anim_props.rigaddon_animation_list_index
            
            if not anim_props.rigaddon_animation_list:
                return {'CANCELLED'}
            
            new_index = current_index + direction
            
            # Check bounds
            if new_index < 0 or new_index >= len(anim_props.rigaddon_animation_list):
                return {'CANCELLED'}
            
            # Move the item
            anim_props.rigaddon_animation_list.move(current_index, new_index)
            anim_props.rigaddon_animation_list_index = new_index
            
            direction_text = "up" if direction < 0 else "down"
            self.report({'INFO'}, f"Moved animation {direction_text}")
            
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Error moving animation: {str(e)}")
            return {'CANCELLED'}

class RIGADDON_OT_SelectAnimationTarget(Operator):
    """Select target object for animation"""
    bl_idname = "rigaddon.select_animation_target"
    bl_label = "Select Target"
    bl_description = "Select the target object for this animation"
    bl_options = {'REGISTER', 'UNDO'}
    
    rigaddon_animation_index: IntProperty(
        name="Animation Index",
        description="Index of animation to update target",
        default=-1
    )
    
    @classmethod
    def poll(cls, context):
        anim_props = context.scene.rigaddon_animation_props
        return len(anim_props.rigaddon_animation_list) > 0
    
    def execute(self, context):
        try:
            anim_props = context.scene.rigaddon_animation_props
            
            # Get animation index
            if self.rigaddon_animation_index >= 0:
                anim_index = self.rigaddon_animation_index
            else:
                anim_index = anim_props.rigaddon_animation_list_index
            
            # Validate index
            if anim_index >= len(anim_props.rigaddon_animation_list):
                self.report({'ERROR'}, "Invalid animation index")
                return {'CANCELLED'}
            
            animation_item = anim_props.rigaddon_animation_list[anim_index]
            
            # Get active object
            if context.active_object:
                animation_item.rigaddon_anim_target_object = context.active_object.name
                self.report({'INFO'}, f"Set target to '{context.active_object.name}'")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "No active object selected")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error setting target: {str(e)}")
            return {'CANCELLED'}

class RIGADDON_OT_JumpToAnimationFrame(Operator):
    """Jump to animation start frame"""
    bl_idname = "rigaddon.jump_to_animation_frame"
    bl_label = "Jump to Frame"
    bl_description = "Jump to animation start frame"
    bl_options = {'REGISTER'}
    
    rigaddon_animation_index: IntProperty(
        name="Animation Index",
        description="Index of animation to jump to",
        default=-1
    )
    
    rigaddon_frame_type: EnumProperty(
        name="Frame Type",
        description="Which frame to jump to",
        items=[
            ('START', 'Start', 'Jump to start frame'),
            ('END', 'End', 'Jump to end frame'),
            ('MIDDLE', 'Middle', 'Jump to middle frame'),
        ],
        default='START'
    )
    
    @classmethod
    def poll(cls, context):
        anim_props = context.scene.rigaddon_animation_props
        return len(anim_props.rigaddon_animation_list) > 0
    
    def execute(self, context):
        try:
            from ..utils.playback import rigaddon_playback_manager
            
            anim_props = context.scene.rigaddon_animation_props
            
            # Get animation index
            if self.rigaddon_animation_index >= 0:
                anim_index = self.rigaddon_animation_index
            else:
                anim_index = anim_props.rigaddon_animation_list_index
            
            # Validate index
            if anim_index >= len(anim_props.rigaddon_animation_list):
                self.report({'ERROR'}, "Invalid animation index")
                return {'CANCELLED'}
            
            animation_item = anim_props.rigaddon_animation_list[anim_index]
            
            # Calculate target frame
            if self.rigaddon_frame_type == 'START':
                target_frame = animation_item.rigaddon_anim_start_frame
            elif self.rigaddon_frame_type == 'END':
                target_frame = animation_item.rigaddon_anim_end_frame
            else:  # MIDDLE
                target_frame = (animation_item.rigaddon_anim_start_frame + animation_item.rigaddon_anim_end_frame) // 2
            
            # Jump to frame
            success = rigaddon_playback_manager.rigaddon_jump_to_frame(target_frame)
            
            if success:
                frame_type_text = self.rigaddon_frame_type.lower()
                self.report({'INFO'}, f"Jumped to {frame_type_text} frame ({target_frame})")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to jump to frame")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error jumping to frame: {str(e)}")
            return {'CANCELLED'}
