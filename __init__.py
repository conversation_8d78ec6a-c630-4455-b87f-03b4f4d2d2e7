# Rigaddon Animation - Professional Animation Addon for Blender 4.3
# Created by: <PERSON><PERSON><PERSON>
# Category: Rigaddon

bl_info = {
    "name": "Rigaddon Animation",
    "author": "<PERSON><PERSON><PERSON>",
    "version": (1, 0, 0),
    "blender": (4, 3, 0),
    "location": "View3D > UI > Rigaddon > Rigaddon Animation",
    "description": "Professional animation tools with preset system and advanced controls",
    "category": "Rigaddon",
    "doc_url": "",
    "tracker_url": "",
}

import bpy
import os
import sys

# Add addon directory to Python path
addon_dir = os.path.dirname(__file__)
if addon_dir not in sys.path:
    sys.path.append(addon_dir)

# Import modules
from . import properties
from . import operators
from . import ui
from . import utils
from . import menus

# Module list for registration
rigaddon_modules = [
    properties,
    operators,
    ui,
    utils,
    menus,
]

def rigaddon_register_modules():
    """Register all Rigaddon Animation modules"""
    for module in rigaddon_modules:
        if hasattr(module, "register"):
            module.register()

def rigaddon_unregister_modules():
    """Unregister all Rigaddon Animation modules"""
    for module in reversed(rigaddon_modules):
        if hasattr(module, "unregister"):
            module.unregister()

def register():
    """Register the Rigaddon Animation addon"""
    try:
        print("Rigaddon Animation: Starting registration...")

        # Register modules one by one with error handling
        for module in rigaddon_modules:
            try:
                if hasattr(module, "register"):
                    module.register()
                    print(f"Rigaddon Animation: Registered {module.__name__}")
                else:
                    print(f"Rigaddon Animation: No register function in {module.__name__}")
            except Exception as e:
                print(f"Rigaddon Animation: Failed to register {module.__name__} - {e}")

        print("Rigaddon Animation: Registration completed")

    except Exception as e:
        print(f"Rigaddon Animation: Registration failed - {e}")
        import traceback
        traceback.print_exc()

def unregister():
    """Unregister the Rigaddon Animation addon"""
    try:
        print("Rigaddon Animation: Starting unregistration...")

        # Unregister modules in reverse order
        for module in reversed(rigaddon_modules):
            try:
                if hasattr(module, "unregister"):
                    module.unregister()
                    print(f"Rigaddon Animation: Unregistered {module.__name__}")
            except Exception as e:
                print(f"Rigaddon Animation: Failed to unregister {module.__name__} - {e}")

        print("Rigaddon Animation: Unregistration completed")

    except Exception as e:
        print(f"Rigaddon Animation: Unregistration failed - {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    register()
