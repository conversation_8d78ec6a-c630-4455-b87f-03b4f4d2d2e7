# Rigaddon Animation - Professional Animation Addon for Blender 4.3
# Created by: <PERSON><PERSON><PERSON>
# Category: Rigaddon

bl_info = {
    "name": "Rigaddon Animation",
    "author": "<PERSON><PERSON><PERSON>",
    "version": (1, 0, 0),
    "blender": (4, 3, 0),
    "location": "View3D > Sidebar > Rigaddon",
    "description": "Professional animation tools with preset system and advanced controls",
    "category": "Rigaddon",
    "doc_url": "",
    "tracker_url": "",
}

import bpy
import os
import sys

# Add addon directory to Python path
addon_dir = os.path.dirname(__file__)
if addon_dir not in sys.path:
    sys.path.append(addon_dir)

# Import modules
from . import properties
from . import operators
from . import ui
from . import utils
from . import menus

# Module list for registration
rigaddon_modules = [
    properties,
    operators,
    ui,
    utils,
    menus,
]

def rigaddon_register_modules():
    """Register all Rigaddon Animation modules"""
    for module in rigaddon_modules:
        if hasattr(module, "register"):
            module.register()

def rigaddon_unregister_modules():
    """Unregister all Rigaddon Animation modules"""
    for module in reversed(rigaddon_modules):
        if hasattr(module, "unregister"):
            module.unregister()

def register():
    """Register the Rigaddon Animation addon"""
    try:
        rigaddon_register_modules()
        print("Rigaddon Animation: Successfully registered")
    except Exception as e:
        print(f"Rigaddon Animation: Registration failed - {e}")

def unregister():
    """Unregister the Rigaddon Animation addon"""
    try:
        rigaddon_unregister_modules()
        print("Rigaddon Animation: Successfully unregistered")
    except Exception as e:
        print(f"Rigaddon Animation: Unregistration failed - {e}")

if __name__ == "__main__":
    register()
