# Rigaddon Animation - Core Animation Engine
# Created by: <PERSON><PERSON><PERSON> Tapangan

import bpy
import bmesh
import mathutils
from mathutils import Vector, Euler, Quaternion
from typing import Dict, List, Any, Optional, Tuple
import math

class RigaddonAnimationCore:
    """Core animation engine for Rigaddon Animation system"""
    
    @staticmethod
    def rigaddon_create_keyframe(obj: bpy.types.Object, frame: int, 
                                property_name: str, value: Any, 
                                interpolation: str = 'BEZIER') -> bool:
        """Create a keyframe for an object property"""
        try:
            if not obj:
                return False
            
            # Set current frame
            bpy.context.scene.frame_set(frame)
            
            # Set the property value
            if property_name == 'location':
                obj.location = Vector(value)
                obj.keyframe_insert(data_path="location", frame=frame)
            elif property_name == 'rotation_euler':
                obj.rotation_euler = Euler(value)
                obj.keyframe_insert(data_path="rotation_euler", frame=frame)
            elif property_name == 'scale':
                obj.scale = Vector(value)
                obj.keyframe_insert(data_path="scale", frame=frame)
            elif property_name == 'alpha':
                # Handle material alpha
                if obj.active_material:
                    if obj.active_material.use_nodes:
                        # Find principled BSDF node
                        for node in obj.active_material.node_tree.nodes:
                            if node.type == 'BSDF_PRINCIPLED':
                                node.inputs['Alpha'].default_value = value
                                node.inputs['Alpha'].keyframe_insert(data_path="default_value", frame=frame)
                                break
                    else:
                        obj.active_material.alpha = value
                        obj.active_material.keyframe_insert(data_path="alpha", frame=frame)
            
            # Set interpolation type
            if obj.animation_data and obj.animation_data.action:
                for fcurve in obj.animation_data.action.fcurves:
                    for keyframe in fcurve.keyframe_points:
                        if keyframe.co[0] == frame:
                            keyframe.interpolation = interpolation
            
            return True
            
        except Exception as e:
            print(f"Rigaddon Animation: Error creating keyframe - {e}")
            return False
    
    @staticmethod
    def rigaddon_apply_preset_to_object(obj: bpy.types.Object, preset_data: Dict[str, Any], 
                                       start_frame: int = 1, custom_duration: Optional[int] = None) -> bool:
        """Apply animation preset to an object"""
        try:
            if not obj or not preset_data:
                return False
            
            duration = custom_duration or preset_data.get('duration', 60)
            properties = preset_data.get('properties', {})
            
            # Clear existing animation data if requested
            if obj.animation_data:
                obj.animation_data_clear()
            
            # Apply keyframes for each property
            for prop_name, prop_data in properties.items():
                keyframes = prop_data.get('keyframes', [])
                
                for keyframe_data in keyframes:
                    frame = start_frame + keyframe_data.get('frame', 1) - 1
                    value = keyframe_data.get('value')
                    interpolation = keyframe_data.get('interpolation', 'BEZIER')
                    
                    if value is not None:
                        RigaddonAnimationCore.rigaddon_create_keyframe(
                            obj, frame, prop_name, value, interpolation
                        )
            
            # Apply easing if specified
            easing = preset_data.get('easing', 'linear')
            RigaddonAnimationCore.rigaddon_apply_easing(obj, easing)
            
            return True
            
        except Exception as e:
            print(f"Rigaddon Animation: Error applying preset - {e}")
            return False
    
    @staticmethod
    def rigaddon_apply_easing(obj: bpy.types.Object, easing_type: str) -> bool:
        """Apply easing to object's animation curves"""
        try:
            if not obj.animation_data or not obj.animation_data.action:
                return False
            
            action = obj.animation_data.action
            
            for fcurve in action.fcurves:
                if easing_type == 'ease_in':
                    for keyframe in fcurve.keyframe_points:
                        keyframe.handle_left_type = 'AUTO'
                        keyframe.handle_right_type = 'AUTO'
                        keyframe.easing = 'EASE_IN'
                        
                elif easing_type == 'ease_out':
                    for keyframe in fcurve.keyframe_points:
                        keyframe.handle_left_type = 'AUTO'
                        keyframe.handle_right_type = 'AUTO'
                        keyframe.easing = 'EASE_OUT'
                        
                elif easing_type == 'ease_in_out':
                    for keyframe in fcurve.keyframe_points:
                        keyframe.handle_left_type = 'AUTO'
                        keyframe.handle_right_type = 'AUTO'
                        keyframe.easing = 'EASE_IN_OUT'
                        
                elif easing_type == 'bounce':
                    for keyframe in fcurve.keyframe_points:
                        keyframe.interpolation = 'BACK'
                        
                elif easing_type == 'linear':
                    for keyframe in fcurve.keyframe_points:
                        keyframe.interpolation = 'LINEAR'
            
            return True
            
        except Exception as e:
            print(f"Rigaddon Animation: Error applying easing - {e}")
            return False
    
    @staticmethod
    def rigaddon_get_object_keyframes(obj: bpy.types.Object, start_frame: int, end_frame: int) -> Dict[str, List[Dict]]:
        """Extract keyframes from object within frame range"""
        keyframes_data = {
            'location': [],
            'rotation_euler': [],
            'scale': [],
            'alpha': []
        }
        
        try:
            if not obj.animation_data or not obj.animation_data.action:
                return keyframes_data
            
            action = obj.animation_data.action
            
            for fcurve in action.fcurves:
                data_path = fcurve.data_path
                array_index = fcurve.array_index
                
                for keyframe in fcurve.keyframe_points:
                    frame = int(keyframe.co[0])
                    if start_frame <= frame <= end_frame:
                        value = keyframe.co[1]
                        interpolation = keyframe.interpolation
                        
                        keyframe_data = {
                            'frame': frame - start_frame + 1,
                            'value': value,
                            'interpolation': interpolation
                        }
                        
                        if 'location' in data_path:
                            if len(keyframes_data['location']) <= array_index:
                                keyframes_data['location'].extend([[] for _ in range(array_index + 1 - len(keyframes_data['location']))])
                            keyframes_data['location'][array_index].append(keyframe_data)
                            
                        elif 'rotation_euler' in data_path:
                            if len(keyframes_data['rotation_euler']) <= array_index:
                                keyframes_data['rotation_euler'].extend([[] for _ in range(array_index + 1 - len(keyframes_data['rotation_euler']))])
                            keyframes_data['rotation_euler'][array_index].append(keyframe_data)
                            
                        elif 'scale' in data_path:
                            if len(keyframes_data['scale']) <= array_index:
                                keyframes_data['scale'].extend([[] for _ in range(array_index + 1 - len(keyframes_data['scale']))])
                            keyframes_data['scale'][array_index].append(keyframe_data)
            
            return keyframes_data
            
        except Exception as e:
            print(f"Rigaddon Animation: Error extracting keyframes - {e}")
            return keyframes_data
    
    @staticmethod
    def rigaddon_scale_animation(obj: bpy.types.Object, scale_factor: float) -> bool:
        """Scale animation values by a factor"""
        try:
            if not obj.animation_data or not obj.animation_data.action:
                return False
            
            action = obj.animation_data.action
            
            for fcurve in action.fcurves:
                if 'location' in fcurve.data_path or 'scale' in fcurve.data_path:
                    for keyframe in fcurve.keyframe_points:
                        keyframe.co[1] *= scale_factor
                        keyframe.handle_left[1] *= scale_factor
                        keyframe.handle_right[1] *= scale_factor
            
            return True
            
        except Exception as e:
            print(f"Rigaddon Animation: Error scaling animation - {e}")
            return False
    
    @staticmethod
    def rigaddon_reverse_animation(obj: bpy.types.Object, start_frame: int, end_frame: int) -> bool:
        """Reverse animation within frame range"""
        try:
            if not obj.animation_data or not obj.animation_data.action:
                return False
            
            action = obj.animation_data.action
            
            for fcurve in action.fcurves:
                keyframes_to_reverse = []
                
                for keyframe in fcurve.keyframe_points:
                    frame = keyframe.co[0]
                    if start_frame <= frame <= end_frame:
                        keyframes_to_reverse.append(keyframe)
                
                # Reverse the keyframes
                for keyframe in keyframes_to_reverse:
                    old_frame = keyframe.co[0]
                    new_frame = end_frame - (old_frame - start_frame)
                    keyframe.co[0] = new_frame
                    
                    # Update handles
                    handle_left_offset = keyframe.handle_left[0] - old_frame
                    handle_right_offset = keyframe.handle_right[0] - old_frame
                    keyframe.handle_left[0] = new_frame - handle_right_offset
                    keyframe.handle_right[0] = new_frame - handle_left_offset
            
            return True
            
        except Exception as e:
            print(f"Rigaddon Animation: Error reversing animation - {e}")
            return False
