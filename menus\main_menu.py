# Rigaddon Animation - Main Menu
# Created by: <PERSON><PERSON><PERSON>

import bpy
from bpy.types import Menu

class RIGADDON_MT_AnimationMenu(Menu):
    """Main animation menu"""
    bl_label = "Rigaddon Animation"
    bl_idname = "RIGADDON_MT_animation_menu"
    
    def draw(self, context):
        layout = self.layout
        
        # Quick actions
        layout.operator("rigaddon.add_animation", text="Add Animation", icon='PLUS')
        layout.separator()
        
        # Playback controls
        layout.operator("rigaddon.play_all_animations", text="Play All Animations", icon='PLAY')
        layout.operator("rigaddon.stop_animation", text="Stop Animation", icon='PAUSE')
        layout.separator()
        
        # Animation management
        layout.operator("rigaddon.duplicate_animation", text="Duplicate Animation", icon='DUPLICATE')
        layout.operator("rigaddon.remove_animation", text="Remove Animation", icon='REMOVE')
        layout.operator("rigaddon.clear_all_animations", text="Clear All Animations", icon='TRASH')
        layout.separator()
        
        # Tools submenu
        layout.menu("RIGADDON_MT_animation_tools", text="Tools", icon='TOOL_SETTINGS')
        
        # Presets submenu
        layout.menu("RIGADDON_MT_animation_presets", text="Presets", icon='PRESET')

class RIGADDON_MT_AnimationToolsMenu(Menu):
    """Animation tools submenu"""
    bl_label = "Animation Tools"
    bl_idname = "RIGADDON_MT_animation_tools"
    
    def draw(self, context):
        layout = self.layout
        
        # Animation modification
        layout.operator("rigaddon.scale_animation", text="Scale Animation", icon='FULLSCREEN_ENTER')
        layout.operator("rigaddon.reverse_animation", text="Reverse Animation", icon='ARROW_LEFTRIGHT')
        layout.separator()
        
        # Playback tools
        layout.operator("rigaddon.toggle_pause", text="Toggle Pause", icon='FRAME_PREV')
        layout.separator()
        
        # Advanced tools
        layout.operator("rigaddon.customize_animation", text="Customize Animation", icon='PREFERENCES')
        layout.operator("rigaddon.update_animation_property", text="Update Properties", icon='FILE_REFRESH')

class RIGADDON_MT_AnimationPresetsMenu(Menu):
    """Animation presets submenu"""
    bl_label = "Animation Presets"
    bl_idname = "RIGADDON_MT_animation_presets"
    
    def draw(self, context):
        layout = self.layout
        
        # Preset categories
        from ..presets.utils_presets import rigaddon_preset_manager
        
        categories = rigaddon_preset_manager.get_rigaddon_preset_categories()
        
        if categories:
            for category in categories:
                category_menu = layout.menu(
                    "RIGADDON_MT_animation_preset_category",
                    text=category.replace('_', ' ').title(),
                    icon='PRESET'
                )
                # Pass category to submenu (this would need custom implementation)
        else:
            layout.label(text="No presets available", icon='INFO')
        
        layout.separator()
        
        # Preset management
        layout.operator("rigaddon.create_custom_preset", text="Create Custom Preset", icon='PLUS')
        layout.operator("rigaddon.reload_presets", text="Reload Presets", icon='FILE_REFRESH')

class RIGADDON_MT_AnimationPresetCategoryMenu(Menu):
    """Preset category submenu"""
    bl_label = "Preset Category"
    bl_idname = "RIGADDON_MT_animation_preset_category"
    
    def draw(self, context):
        layout = self.layout
        
        # This would be dynamically populated based on selected category
        # For now, show a placeholder
        layout.label(text="Preset items would be here", icon='INFO')

def rigaddon_menu_draw_func(self, context):
    """Function to draw Rigaddon menu in existing menus"""
    self.layout.separator()
    self.layout.menu("RIGADDON_MT_animation_menu", text="Rigaddon Animation", icon='SEQUENCE')

# Menu registration functions
def rigaddon_register_menus():
    """Register menus in Blender interface"""
    try:
        # Add to View3D header menu
        bpy.types.VIEW3D_MT_object.append(rigaddon_menu_draw_func)
        
        # Add to Animation menu if it exists
        if hasattr(bpy.types, 'DOPESHEET_MT_editor_menus'):
            bpy.types.DOPESHEET_MT_editor_menus.append(rigaddon_menu_draw_func)
        
        print("Rigaddon Animation: Menus registered successfully")
        
    except Exception as e:
        print(f"Rigaddon Animation: Error registering menus - {e}")

def rigaddon_unregister_menus():
    """Unregister menus from Blender interface"""
    try:
        # Remove from View3D header menu
        bpy.types.VIEW3D_MT_object.remove(rigaddon_menu_draw_func)
        
        # Remove from Animation menu if it exists
        if hasattr(bpy.types, 'DOPESHEET_MT_editor_menus'):
            bpy.types.DOPESHEET_MT_editor_menus.remove(rigaddon_menu_draw_func)
        
        print("Rigaddon Animation: Menus unregistered successfully")
        
    except Exception as e:
        print(f"Rigaddon Animation: Error unregistering menus - {e}")

# Context menu for animation list items
class RIGADDON_MT_AnimationListContext(Menu):
    """Context menu for animation list items"""
    bl_label = "Animation Context"
    bl_idname = "RIGADDON_MT_animation_list_context"
    
    def draw(self, context):
        layout = self.layout
        
        anim_props = context.scene.rigaddon_animation_props
        
        if anim_props.rigaddon_animation_list and anim_props.rigaddon_animation_list_index < len(anim_props.rigaddon_animation_list):
            selected_anim = anim_props.rigaddon_animation_list[anim_props.rigaddon_animation_list_index]
            
            # Play controls
            layout.operator("rigaddon.play_animation", text="Play", icon='PLAY')
            layout.operator("rigaddon.play_animation_loop", text="Play Loop", icon='FILE_REFRESH')
            layout.operator("rigaddon.play_from_animation", text="Play From Here", icon='PLAY_REVERSE')
            layout.separator()
            
            # Edit controls
            layout.operator("rigaddon.duplicate_animation", text="Duplicate", icon='DUPLICATE')
            layout.operator("rigaddon.select_animation_target", text="Set Target", icon='OBJECT_DATA')
            layout.separator()
            
            # Frame navigation
            jump_menu = layout.menu("RIGADDON_MT_animation_jump_menu", text="Jump to Frame", icon='FRAME_NEXT')
            layout.separator()
            
            # Toggle enabled
            if selected_anim.rigaddon_anim_enabled:
                layout.operator("rigaddon.toggle_animation_enabled", text="Disable", icon='HIDE_ON')
            else:
                layout.operator("rigaddon.toggle_animation_enabled", text="Enable", icon='HIDE_OFF')
            
            layout.separator()
            layout.operator("rigaddon.remove_animation", text="Remove", icon='REMOVE')

class RIGADDON_MT_AnimationJumpMenu(Menu):
    """Jump to frame submenu"""
    bl_label = "Jump to Frame"
    bl_idname = "RIGADDON_MT_animation_jump_menu"
    
    def draw(self, context):
        layout = self.layout
        
        # Jump options
        start_op = layout.operator("rigaddon.jump_to_animation_frame", text="Start Frame", icon='FRAME_PREV')
        start_op.rigaddon_frame_type = 'START'
        
        middle_op = layout.operator("rigaddon.jump_to_animation_frame", text="Middle Frame", icon='FRAME_NEXT')
        middle_op.rigaddon_frame_type = 'MIDDLE'
        
        end_op = layout.operator("rigaddon.jump_to_animation_frame", text="End Frame", icon='FRAME_NEXT')
        end_op.rigaddon_frame_type = 'END'
