# Rigaddon Animation - Menus Module
# Created by: <PERSON><PERSON><PERSON>

from .main_menu import (
    RIGADDON_MT_AnimationMenu,
    RIGADDON_MT_AnimationToolsMenu,
    RIGADDON_MT_AnimationPresetsMenu,
    RIGADDON_MT_AnimationPresetCategoryMenu,
    RIG<PERSON>DON_MT_AnimationListContext,
    RIG<PERSON>D<PERSON>_MT_AnimationJumpMenu,
    rigaddon_register_menus,
    rigaddon_unregister_menus,
)

rigaddon_menu_classes = [
    RIGADDON_MT_AnimationMenu,
    RIGADDON_MT_AnimationToolsMenu,
    RIGADDON_MT_AnimationPresetsMenu,
    RIG<PERSON><PERSON><PERSON>_MT_AnimationPresetCategoryMenu,
    R<PERSON><PERSON><PERSON><PERSON>_MT_AnimationListContext,
    RIGADDON_MT_AnimationJumpMenu,
]

def register():
    """Register menu classes"""
    from bpy.utils import register_class
    for cls in rigaddon_menu_classes:
        register_class(cls)

    # Register menu integration
    rigaddon_register_menus()

def unregister():
    """Unregister menu classes"""
    # Unregister menu integration
    rigaddon_unregister_menus()

    from bpy.utils import unregister_class
    for cls in reversed(rigaddon_menu_classes):
        unregister_class(cls)
