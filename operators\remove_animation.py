# Rigaddon Animation - Remove Animation Operator
# Created by: <PERSON><PERSON><PERSON>

import bpy
from bpy.types import Operator
from bpy.props import StringProperty, IntProperty, BoolProperty, EnumProperty

class RIGADDON_OT_RemoveAnimation(Operator):
    """Remove animation from list and optionally from object"""
    bl_idname = "rigaddon.remove_animation"
    bl_label = "Remove Animation"
    bl_description = "Remove animation from list and optionally from object"
    bl_options = {'REGISTER', 'UNDO'}
    
    rigaddon_animation_index: IntProperty(
        name="Animation Index",
        description="Index of animation to remove",
        default=-1
    )
    
    rigaddon_remove_keyframes: BoolProperty(
        name="Remove Keyframes",
        description="Also remove keyframes from the object",
        default=True
    )
    
    rigaddon_confirm: BoolProperty(
        name="Confirm",
        description="Confirm removal",
        default=False
    )
    
    @classmethod
    def poll(cls, context):
        anim_props = context.scene.rigaddon_animation_props
        return len(anim_props.rigaddon_animation_list) > 0
    
    def execute(self, context):
        try:
            anim_props = context.scene.rigaddon_animation_props
            
            # Get animation index
            if self.rigaddon_animation_index >= 0:
                anim_index = self.rigaddon_animation_index
            else:
                anim_index = anim_props.rigaddon_animation_list_index
            
            # Validate index
            if anim_index >= len(anim_props.rigaddon_animation_list):
                self.report({'ERROR'}, "Invalid animation index")
                return {'CANCELLED'}
            
            animation_item = anim_props.rigaddon_animation_list[anim_index]
            anim_name = animation_item.rigaddon_anim_name
            target_object_name = animation_item.rigaddon_anim_target_object
            
            # Remove keyframes from object if requested
            if self.rigaddon_remove_keyframes:
                target_obj = bpy.data.objects.get(target_object_name)
                if target_obj:
                    success = self.rigaddon_remove_keyframes_from_object(
                        target_obj, 
                        animation_item.rigaddon_anim_start_frame,
                        animation_item.rigaddon_anim_end_frame
                    )
                    if not success:
                        self.report({'WARNING'}, f"Failed to remove keyframes from object '{target_object_name}'")
                else:
                    self.report({'WARNING'}, f"Target object '{target_object_name}' not found")
            
            # Remove from list
            anim_props.rigaddon_animation_list.remove(anim_index)
            
            # Update list index
            if anim_props.rigaddon_animation_list_index >= len(anim_props.rigaddon_animation_list):
                anim_props.rigaddon_animation_list_index = max(0, len(anim_props.rigaddon_animation_list) - 1)
            
            self.report({'INFO'}, f"Removed animation '{anim_name}'")
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Error removing animation: {str(e)}")
            return {'CANCELLED'}
    
    def rigaddon_remove_keyframes_from_object(self, obj: bpy.types.Object, start_frame: int, end_frame: int) -> bool:
        """Remove keyframes from object within frame range"""
        try:
            if not obj.animation_data or not obj.animation_data.action:
                return True  # No animation data to remove
            
            action = obj.animation_data.action
            
            # Collect keyframes to remove
            keyframes_to_remove = []
            
            for fcurve in action.fcurves:
                for i, keyframe in enumerate(fcurve.keyframe_points):
                    frame = int(keyframe.co[0])
                    if start_frame <= frame <= end_frame:
                        keyframes_to_remove.append((fcurve, i))
            
            # Remove keyframes (in reverse order to maintain indices)
            for fcurve, keyframe_index in reversed(keyframes_to_remove):
                fcurve.keyframe_points.remove(fcurve.keyframe_points[keyframe_index])
            
            # Remove empty fcurves
            fcurves_to_remove = []
            for fcurve in action.fcurves:
                if len(fcurve.keyframe_points) == 0:
                    fcurves_to_remove.append(fcurve)
            
            for fcurve in fcurves_to_remove:
                action.fcurves.remove(fcurve)
            
            # Remove action if empty
            if len(action.fcurves) == 0:
                bpy.data.actions.remove(action)
                obj.animation_data_clear()
            
            return True
            
        except Exception as e:
            print(f"Rigaddon Animation: Error removing keyframes - {e}")
            return False
    
    def invoke(self, context, event):
        if not self.rigaddon_confirm:
            return context.window_manager.invoke_props_dialog(self)
        return self.execute(context)
    
    def draw(self, context):
        layout = self.layout
        
        anim_props = context.scene.rigaddon_animation_props
        if anim_props.rigaddon_animation_list_index < len(anim_props.rigaddon_animation_list):
            animation_item = anim_props.rigaddon_animation_list[anim_props.rigaddon_animation_list_index]
            
            layout.label(text=f"Remove animation '{animation_item.rigaddon_anim_name}'?")
            layout.label(text=f"Target: {animation_item.rigaddon_anim_target_object}")
            layout.separator()
            layout.prop(self, "rigaddon_remove_keyframes")

class RIGADDON_OT_ClearAllAnimations(Operator):
    """Clear all animations from list"""
    bl_idname = "rigaddon.clear_all_animations"
    bl_label = "Clear All Animations"
    bl_description = "Clear all animations from the list"
    bl_options = {'REGISTER', 'UNDO'}
    
    rigaddon_remove_keyframes: BoolProperty(
        name="Remove Keyframes",
        description="Also remove keyframes from all objects",
        default=False
    )
    
    @classmethod
    def poll(cls, context):
        anim_props = context.scene.rigaddon_animation_props
        return len(anim_props.rigaddon_animation_list) > 0
    
    def execute(self, context):
        try:
            anim_props = context.scene.rigaddon_animation_props
            
            removed_count = 0
            
            # Remove keyframes from objects if requested
            if self.rigaddon_remove_keyframes:
                for animation_item in anim_props.rigaddon_animation_list:
                    target_obj = bpy.data.objects.get(animation_item.rigaddon_anim_target_object)
                    if target_obj:
                        # Remove all animation data from object
                        if target_obj.animation_data:
                            target_obj.animation_data_clear()
            
            # Clear the list
            removed_count = len(anim_props.rigaddon_animation_list)
            anim_props.rigaddon_animation_list.clear()
            anim_props.rigaddon_animation_list_index = 0
            
            self.report({'INFO'}, f"Cleared {removed_count} animations")
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Error clearing animations: {str(e)}")
            return {'CANCELLED'}
    
    def invoke(self, context, event):
        return context.window_manager.invoke_confirm(self, event)
    
    def draw(self, context):
        layout = self.layout
        layout.prop(self, "rigaddon_remove_keyframes")

class RIGADDON_OT_DuplicateAnimation(Operator):
    """Duplicate selected animation"""
    bl_idname = "rigaddon.duplicate_animation"
    bl_label = "Duplicate Animation"
    bl_description = "Duplicate the selected animation"
    bl_options = {'REGISTER', 'UNDO'}
    
    rigaddon_animation_index: IntProperty(
        name="Animation Index",
        description="Index of animation to duplicate",
        default=-1
    )
    
    rigaddon_new_name: StringProperty(
        name="New Name",
        description="Name for the duplicated animation",
        default=""
    )
    
    rigaddon_frame_offset: IntProperty(
        name="Frame Offset",
        description="Frame offset for the duplicated animation",
        default=0
    )
    
    @classmethod
    def poll(cls, context):
        anim_props = context.scene.rigaddon_animation_props
        return len(anim_props.rigaddon_animation_list) > 0
    
    def execute(self, context):
        try:
            anim_props = context.scene.rigaddon_animation_props
            
            # Get animation index
            if self.rigaddon_animation_index >= 0:
                anim_index = self.rigaddon_animation_index
            else:
                anim_index = anim_props.rigaddon_animation_list_index
            
            # Validate index
            if anim_index >= len(anim_props.rigaddon_animation_list):
                self.report({'ERROR'}, "Invalid animation index")
                return {'CANCELLED'}
            
            source_item = anim_props.rigaddon_animation_list[anim_index]
            
            # Create duplicate
            new_item = anim_props.rigaddon_animation_list.add()
            
            # Copy properties
            new_item.rigaddon_anim_name = self.rigaddon_new_name or f"{source_item.rigaddon_anim_name} Copy"
            new_item.rigaddon_anim_description = source_item.rigaddon_anim_description
            new_item.rigaddon_anim_duration = source_item.rigaddon_anim_duration
            new_item.rigaddon_anim_start_frame = source_item.rigaddon_anim_start_frame + self.rigaddon_frame_offset
            new_item.rigaddon_anim_end_frame = source_item.rigaddon_anim_end_frame + self.rigaddon_frame_offset
            new_item.rigaddon_anim_loop = source_item.rigaddon_anim_loop
            new_item.rigaddon_anim_enabled = source_item.rigaddon_anim_enabled
            new_item.rigaddon_anim_target_object = source_item.rigaddon_anim_target_object
            new_item.rigaddon_anim_preset_category = source_item.rigaddon_anim_preset_category
            new_item.rigaddon_anim_preset_name = source_item.rigaddon_anim_preset_name
            new_item.rigaddon_anim_easing = source_item.rigaddon_anim_easing
            new_item.rigaddon_anim_use_location = source_item.rigaddon_anim_use_location
            new_item.rigaddon_anim_use_rotation = source_item.rigaddon_anim_use_rotation
            new_item.rigaddon_anim_use_scale = source_item.rigaddon_anim_use_scale
            new_item.rigaddon_anim_use_alpha = source_item.rigaddon_anim_use_alpha
            new_item.rigaddon_anim_speed_multiplier = source_item.rigaddon_anim_speed_multiplier
            
            # Update list index to show new animation
            anim_props.rigaddon_animation_list_index = len(anim_props.rigaddon_animation_list) - 1
            
            self.report({'INFO'}, f"Duplicated animation '{source_item.rigaddon_anim_name}'")
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Error duplicating animation: {str(e)}")
            return {'CANCELLED'}
    
    def invoke(self, context, event):
        anim_props = context.scene.rigaddon_animation_props
        if anim_props.rigaddon_animation_list_index < len(anim_props.rigaddon_animation_list):
            source_item = anim_props.rigaddon_animation_list[anim_props.rigaddon_animation_list_index]
            self.rigaddon_new_name = f"{source_item.rigaddon_anim_name} Copy"
            self.rigaddon_frame_offset = source_item.rigaddon_anim_duration
        
        return context.window_manager.invoke_props_dialog(self)
    
    def draw(self, context):
        layout = self.layout
        layout.prop(self, "rigaddon_new_name")
        layout.prop(self, "rigaddon_frame_offset")
