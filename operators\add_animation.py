# Rigaddon Animation - Add Animation Operator
# Created by: <PERSON><PERSON><PERSON>

import bpy
from bpy.types import Operator
from bpy.props import StringProperty, IntProperty, BoolProperty, EnumProperty

class RIGADDON_OT_AddAnimation(Operator):
    """Add animation from preset to selected objects"""
    bl_idname = "rigaddon.add_animation"
    bl_label = "Add Animation"
    bl_description = "Add animation from preset to selected objects"
    bl_options = {'REGISTER', 'UNDO'}
    
    rigaddon_preset_category: StringProperty(
        name="Category",
        description="Preset category",
        default=""
    )
    
    rigaddon_preset_name: StringProperty(
        name="Preset",
        description="Preset name",
        default=""
    )
    
    rigaddon_custom_name: StringProperty(
        name="Animation Name",
        description="Custom name for this animation",
        default=""
    )
    
    rigaddon_start_frame: IntProperty(
        name="Start Frame",
        description="Starting frame for animation",
        default=1,
        min=1
    )
    
    rigaddon_custom_duration: IntProperty(
        name="Duration",
        description="Custom duration in frames (0 = use preset default)",
        default=0,
        min=0
    )
    
    rigaddon_apply_to_all: BoolProperty(
        name="Apply to All Selected",
        description="Apply animation to all selected objects",
        default=True
    )
    
    rigaddon_frame_offset: IntProperty(
        name="Frame Offset",
        description="Frame offset between objects",
        default=0,
        min=0
    )
    
    @classmethod
    def poll(cls, context):
        return len(context.selected_objects) > 0
    
    def execute(self, context):
        try:
            from ..presets.utils_presets import rigaddon_preset_manager
            from ..utils.animation_core import RigaddonAnimationCore
            
            # Get preset data
            if not self.rigaddon_preset_category or not self.rigaddon_preset_name:
                # Try to get from scene properties
                preset_props = context.scene.rigaddon_preset_props
                if preset_props.rigaddon_selected_category != 'NONE' and preset_props.rigaddon_selected_preset != 'NONE':
                    self.rigaddon_preset_category = preset_props.rigaddon_selected_category
                    self.rigaddon_preset_name = preset_props.rigaddon_selected_preset
                else:
                    self.report({'ERROR'}, "No preset selected")
                    return {'CANCELLED'}
            
            preset_data = rigaddon_preset_manager.get_rigaddon_preset_by_name(
                self.rigaddon_preset_category, self.rigaddon_preset_name
            )
            
            if not preset_data:
                self.report({'ERROR'}, f"Preset '{self.rigaddon_preset_name}' not found in category '{self.rigaddon_preset_category}'")
                return {'CANCELLED'}
            
            # Get selected objects
            selected_objects = context.selected_objects.copy()
            if not selected_objects:
                self.report({'ERROR'}, "No objects selected")
                return {'CANCELLED'}
            
            # Get animation properties
            anim_props = context.scene.rigaddon_animation_props
            
            # Apply animation to objects
            added_count = 0
            current_offset = 0
            
            for obj in selected_objects:
                if not self.rigaddon_apply_to_all and obj != context.active_object:
                    continue
                
                # Calculate start frame with offset
                start_frame = self.rigaddon_start_frame + current_offset
                
                # Apply preset to object
                duration = self.rigaddon_custom_duration if self.rigaddon_custom_duration > 0 else None
                success = RigaddonAnimationCore.rigaddon_apply_preset_to_object(
                    obj, preset_data, start_frame, duration
                )
                
                if success:
                    # Add to animation list
                    anim_item = anim_props.rigaddon_animation_list.add()
                    
                    # Set animation properties
                    anim_item.rigaddon_anim_name = self.rigaddon_custom_name or f"{preset_data.get('name', 'Animation')} - {obj.name}"
                    anim_item.rigaddon_anim_description = preset_data.get('description', '')
                    anim_item.rigaddon_anim_duration = duration or preset_data.get('duration', 60)
                    anim_item.rigaddon_anim_start_frame = start_frame
                    anim_item.rigaddon_anim_end_frame = start_frame + anim_item.rigaddon_anim_duration - 1
                    anim_item.rigaddon_anim_loop = preset_data.get('loop', False)
                    anim_item.rigaddon_anim_target_object = obj.name
                    anim_item.rigaddon_anim_preset_category = self.rigaddon_preset_category
                    anim_item.rigaddon_anim_preset_name = self.rigaddon_preset_name
                    anim_item.rigaddon_anim_easing = preset_data.get('easing', 'ease_in_out')
                    
                    # Set animation properties based on preset
                    properties = preset_data.get('properties', {})
                    anim_item.rigaddon_anim_use_location = 'location' in properties
                    anim_item.rigaddon_anim_use_rotation = 'rotation_euler' in properties
                    anim_item.rigaddon_anim_use_scale = 'scale' in properties
                    anim_item.rigaddon_anim_use_alpha = 'alpha' in properties
                    
                    added_count += 1
                    
                    # Update list index to show new animation
                    anim_props.rigaddon_animation_list_index = len(anim_props.rigaddon_animation_list) - 1
                
                # Apply frame offset for next object
                if self.rigaddon_apply_to_all:
                    current_offset += self.rigaddon_frame_offset
            
            if added_count > 0:
                self.report({'INFO'}, f"Successfully added animation to {added_count} object(s)")
                
                # Update timeline to show animation range
                if anim_props.rigaddon_animation_list:
                    min_start = min(item.rigaddon_anim_start_frame for item in anim_props.rigaddon_animation_list)
                    max_end = max(item.rigaddon_anim_end_frame for item in anim_props.rigaddon_animation_list)
                    context.scene.frame_start = min_start
                    context.scene.frame_end = max_end
                
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to add animation to any objects")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error adding animation: {str(e)}")
            return {'CANCELLED'}
    
    def invoke(self, context, event):
        # Get current preset selection
        preset_props = context.scene.rigaddon_preset_props
        if preset_props.rigaddon_selected_category != 'NONE' and preset_props.rigaddon_selected_preset != 'NONE':
            self.rigaddon_preset_category = preset_props.rigaddon_selected_category
            self.rigaddon_preset_name = preset_props.rigaddon_selected_preset
            
            # Set custom name if provided
            if preset_props.rigaddon_custom_name:
                self.rigaddon_custom_name = preset_props.rigaddon_custom_name
            
            # Set custom duration if provided
            if preset_props.rigaddon_custom_duration > 0:
                self.rigaddon_custom_duration = preset_props.rigaddon_custom_duration
            
            # Set other properties
            self.rigaddon_apply_to_all = preset_props.rigaddon_apply_to_selected
            self.rigaddon_frame_offset = preset_props.rigaddon_offset_frames
        
        return self.execute(context)
    
    def draw(self, context):
        layout = self.layout
        
        layout.prop(self, "rigaddon_custom_name")
        layout.prop(self, "rigaddon_start_frame")
        layout.prop(self, "rigaddon_custom_duration")
        
        layout.separator()
        
        layout.prop(self, "rigaddon_apply_to_all")
        if self.rigaddon_apply_to_all:
            layout.prop(self, "rigaddon_frame_offset")
