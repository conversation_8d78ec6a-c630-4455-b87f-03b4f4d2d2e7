# Rigaddon Animation - Professional Animation Addon for Blender 4.3

**Created by: <PERSON><PERSON><PERSON>**  
**Category: Rigaddon**  
**Version: 1.0.0**

## Overview

Rigaddon Animation is a comprehensive animation addon for Blender 4.3 that provides professional animation tools with an extensive preset system and advanced controls. The addon is designed to streamline the animation workflow while offering powerful features for both beginners and professionals.

## Features

### 🎯 Core Features
- **Modular Architecture**: Clean, maintainable code structure
- **JSON Preset System**: Easily customizable animation presets
- **Professional UI**: Tabbed panels with UIList for organized workflow
- **Animation Management**: Complete animation list with controls
- **Advanced Playback**: Multiple playback modes and controls

### 🎨 Animation Presets
- **Basic Movements**: Bounce, Slide In, Fade In
- **Rotations**: Spin, Wobble
- **Scaling**: Pulse, Grow
- **Complex Animations**: Float, Combined movements
- **Custom Presets**: Create and save your own animations

### 🎮 Playback Controls
- **Single Play**: Play animation once
- **Loop Play**: Continuous loop playback
- **Play From**: Start from specific animation
- **Sequence Play**: Play multiple animations
- **Advanced Controls**: Pause, stop, step frame by frame

### 🔧 Advanced Features
- **Animation Blending**: Blend two animations with custom factors
- **Path Animation**: Create animations following curves
- **Noise Animation**: Procedural organic movement
- **Physics Simulation**: Gravity and pendulum simulations
- **Batch Operations**: Apply animations to multiple objects

### ⚙️ Professional Tools
- **Custom Controls**: Fine-tune animation properties
- **Easing Curves**: Multiple interpolation types
- **Speed Control**: Global and per-animation speed multipliers
- **Frame Management**: Precise frame control and navigation
- **Backup/Restore**: Save and restore preset configurations

## Installation

1. Download the addon files
2. In Blender, go to Edit > Preferences > Add-ons
3. Click "Install..." and select the addon folder
4. Enable "Rigaddon Animation" in the addon list
5. The addon will appear in the 3D Viewport sidebar under "Rigaddon" tab

## Usage

### Basic Workflow

1. **Select Objects**: Choose the objects you want to animate
2. **Choose Preset**: Select category and preset from the presets panel
3. **Customize**: Adjust duration, name, and other settings
4. **Add Animation**: Click "Add Animation" to apply
5. **Control Playback**: Use the animation list controls to play, loop, or modify

### Animation List Controls

- **Play**: Play animation once
- **Loop**: Play animation continuously
- **Play From**: Start timeline from this animation
- **Enable/Disable**: Toggle animation on/off
- **Advanced**: Access detailed controls

### Advanced Features

#### Custom Presets
1. Animate your object manually
2. Enable "Create Mode" in Animation Tools
3. Set frame range and metadata
4. Click "Create Preset" to save

#### Animation Blending
1. Create two different animations
2. Use "Blend Animations" operator
3. Adjust blend factor (0-1)
4. Apply blended result

#### Path Animation
1. Create a curve object for the path
2. Select both object and curve
3. Use "Create Path Animation"
4. Adjust duration and settings

## File Structure

```
Rigaddon Animation/
├── __init__.py              # Main addon file
├── presets/                 # Preset system
│   ├── anim_presets.json   # Animation presets data
│   └── utils_presets.py    # Preset management
├── ui/                      # User interface
│   ├── panels.py           # Main UI panels
│   └── lists.py            # Animation list UI
├── operators/               # Blender operators
│   ├── add_animation.py    # Add animation functionality
│   ├── play_animation.py   # Playback controls
│   ├── remove_animation.py # Remove/manage animations
│   ├── custom_controls.py  # Advanced controls
│   ├── preset_operators.py # Preset management
│   └── advanced_operators.py # Advanced features
├── properties/              # Blender properties
│   ├── anim_properties.py  # Animation data
│   └── preset_properties.py # Preset settings
├── utils/                   # Utility modules
│   ├── json_loader.py      # JSON handling
│   ├── animation_core.py   # Core animation engine
│   ├── playback.py         # Playback management
│   └── advanced_features.py # Advanced animation tools
└── menus/                   # Context menus
    └── main_menu.py        # Main menu integration
```

## Customization

### Adding Custom Presets

Presets are stored in `presets/anim_presets.json`. You can:
- Add new categories
- Create custom animations
- Modify existing presets
- Use the built-in preset creation tools

### Modifying UI

The UI is modular and can be customized by editing files in the `ui/` directory:
- `panels.py`: Main interface panels
- `lists.py`: Animation list display

## Technical Details

### Requirements
- Blender 4.3 or higher
- Python 3.x (included with Blender)

### Performance
- Optimized for real-time preview
- Efficient keyframe management
- Minimal memory footprint

### Compatibility
- Works with all object types
- Compatible with existing animations
- Non-destructive workflow

## Troubleshooting

### Common Issues

**Addon not appearing**: Check Blender version compatibility
**Presets not loading**: Verify JSON file integrity
**Animation not playing**: Check object selection and animation data
**Performance issues**: Reduce animation complexity or duration

### Support

For issues and feature requests, please contact the developer or check the addon documentation.

## License

This addon is created by Rigel Tapangan for professional animation workflow in Blender 4.3.

## Changelog

### Version 1.0.0
- Initial release
- Complete preset system
- Professional UI
- Advanced animation features
- Comprehensive playback controls

---

**Rigaddon Animation** - Bringing professional animation tools to Blender 4.3
