# Rigaddon Animation - Configuration
# Created by: <PERSON><PERSON><PERSON>

"""
Configuration file for Rigaddon Animation addon
Contains constants, settings, and configuration options
"""

# Addon Information
RIGADDON_ADDON_INFO = {
    "name": "Rigaddon Animation",
    "author": "<PERSON><PERSON><PERSON>",
    "version": (1, 0, 0),
    "blender": (4, 3, 0),
    "location": "View3D > Sidebar > Rigaddon",
    "description": "Professional animation tools with preset system and advanced controls",
    "category": "Rigaddon",
    "doc_url": "",
    "tracker_url": "",
}

# File Paths
RIGADDON_PATHS = {
    "presets_file": "presets/anim_presets.json",
    "backup_dir": "backups",
    "temp_dir": "temp",
    "icons_dir": "icons",
}

# Default Settings
RIGADDON_DEFAULTS = {
    "frame_rate": 24,
    "default_duration": 60,
    "auto_keyframe": True,
    "use_fake_user": True,
    "global_speed": 1.0,
    "playback_mode": "SINGLE",
    "show_advanced": False,
    "auto_preview": True,
}

# Animation Constants
RIGADDON_ANIMATION = {
    "min_duration": 1,
    "max_duration": 1000,
    "min_frame": 1,
    "max_frame": 999999,
    "default_interpolation": "BEZIER",
    "default_easing": "ease_in_out",
}

# UI Constants
RIGADDON_UI = {
    "panel_category": "Rigaddon",
    "list_rows": 4,
    "button_scale": 1.5,
    "icon_size": "SMALL",
    "spacing": 2,
    "panel_width": 300,
}

# Preset Categories
RIGADDON_PRESET_CATEGORIES = [
    "basic_movements",
    "rotations", 
    "scaling",
    "complex",
    "custom"
]

# Supported Properties
RIGADDON_PROPERTIES = [
    "location",
    "rotation_euler",
    "scale",
    "alpha"
]

# Interpolation Types
RIGADDON_INTERPOLATION_TYPES = [
    ("BEZIER", "Bezier", "Smooth bezier interpolation"),
    ("LINEAR", "Linear", "Linear interpolation"),
    ("CONSTANT", "Constant", "No interpolation"),
]

# Easing Types
RIGADDON_EASING_TYPES = [
    ("linear", "Linear", "Linear easing"),
    ("ease_in", "Ease In", "Ease in"),
    ("ease_out", "Ease Out", "Ease out"),
    ("ease_in_out", "Ease In Out", "Ease in and out"),
    ("bounce", "Bounce", "Bounce easing"),
    ("elastic", "Elastic", "Elastic easing"),
]

# Playback Modes
RIGADDON_PLAYBACK_MODES = [
    ("SINGLE", "Single", "Play single animation"),
    ("SEQUENCE", "Sequence", "Play animations in sequence"),
    ("PARALLEL", "Parallel", "Play animations in parallel"),
]

# Physics Simulation Types
RIGADDON_PHYSICS_TYPES = [
    ("gravity_fall", "Gravity Fall", "Simple gravity fall simulation"),
    ("pendulum", "Pendulum", "Pendulum motion simulation"),
]

# Noise Animation Properties
RIGADDON_NOISE_PROPERTIES = [
    ("location", "Location", "Animate location"),
    ("rotation", "Rotation", "Animate rotation"),
    ("scale", "Scale", "Animate scale"),
]

# Batch Offset Types
RIGADDON_OFFSET_TYPES = [
    ("time", "Time Offset", "Linear time offset"),
    ("random", "Random Offset", "Random time offset"),
    ("wave", "Wave Offset", "Wave pattern offset"),
    ("none", "No Offset", "No offset between objects"),
]

# Error Messages
RIGADDON_ERRORS = {
    "no_object": "No object selected",
    "no_preset": "No preset selected",
    "no_animation": "No animation found",
    "invalid_index": "Invalid animation index",
    "preset_not_found": "Preset not found",
    "target_not_found": "Target object not found",
    "invalid_duration": "Invalid duration",
    "invalid_frame": "Invalid frame number",
    "file_not_found": "File not found",
    "json_error": "JSON parsing error",
    "animation_error": "Animation creation error",
    "playback_error": "Playback error",
}

# Success Messages
RIGADDON_SUCCESS = {
    "animation_added": "Animation added successfully",
    "animation_removed": "Animation removed successfully",
    "animation_played": "Animation playing",
    "animation_stopped": "Animation stopped",
    "preset_created": "Preset created successfully",
    "preset_loaded": "Presets loaded successfully",
    "backup_created": "Backup created successfully",
    "backup_restored": "Backup restored successfully",
}

# Warning Messages
RIGADDON_WARNINGS = {
    "no_keyframes": "No keyframes to remove",
    "animation_disabled": "Animation is disabled",
    "no_curve": "No curve object selected",
    "empty_list": "Animation list is empty",
    "overwrite_warning": "This will overwrite existing data",
}

# Debug Settings
RIGADDON_DEBUG = {
    "enabled": False,
    "verbose": False,
    "log_file": "rigaddon_debug.log",
    "print_errors": True,
    "print_warnings": True,
    "print_info": True,
}

# Performance Settings
RIGADDON_PERFORMANCE = {
    "max_keyframes": 1000,
    "max_objects": 100,
    "update_frequency": 0.1,
    "cache_size": 50,
    "auto_cleanup": True,
}

# Validation Rules
RIGADDON_VALIDATION = {
    "min_name_length": 1,
    "max_name_length": 64,
    "allowed_characters": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_- ",
    "reserved_names": ["None", "NONE", "none"],
}

# File Extensions
RIGADDON_FILE_EXTENSIONS = {
    "preset": ".json",
    "backup": ".json",
    "export": ".json",
    "log": ".log",
}

# Version Compatibility
RIGADDON_COMPATIBILITY = {
    "min_blender_version": (4, 3, 0),
    "max_blender_version": (5, 0, 0),
    "python_version": (3, 10),
}

def rigaddon_get_config(category: str, key: str = None):
    """Get configuration value"""
    config_map = {
        "addon": RIGADDON_ADDON_INFO,
        "paths": RIGADDON_PATHS,
        "defaults": RIGADDON_DEFAULTS,
        "animation": RIGADDON_ANIMATION,
        "ui": RIGADDON_UI,
        "errors": RIGADDON_ERRORS,
        "success": RIGADDON_SUCCESS,
        "warnings": RIGADDON_WARNINGS,
        "debug": RIGADDON_DEBUG,
        "performance": RIGADDON_PERFORMANCE,
        "validation": RIGADDON_VALIDATION,
        "compatibility": RIGADDON_COMPATIBILITY,
    }
    
    if category in config_map:
        if key:
            return config_map[category].get(key)
        return config_map[category]
    
    return None

def rigaddon_validate_config():
    """Validate configuration settings"""
    try:
        # Check required settings
        required_keys = ["name", "author", "version"]
        for key in required_keys:
            if key not in RIGADDON_ADDON_INFO:
                print(f"Rigaddon Animation: Missing required config key: {key}")
                return False
        
        # Check version format
        if not isinstance(RIGADDON_ADDON_INFO["version"], tuple) or len(RIGADDON_ADDON_INFO["version"]) != 3:
            print("Rigaddon Animation: Invalid version format")
            return False
        
        print("Rigaddon Animation: Configuration validated successfully")
        return True
        
    except Exception as e:
        print(f"Rigaddon Animation: Configuration validation error - {e}")
        return False
