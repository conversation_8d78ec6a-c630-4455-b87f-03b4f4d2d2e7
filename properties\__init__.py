# Rigaddon Animation - Properties Module
# Created by: <PERSON><PERSON><PERSON>

import bpy
from .anim_properties import (
    RigaddonAnimationItem,
    RigaddonAnimationProperties,
)
from .preset_properties import RigaddonPresetProperties

rigaddon_property_classes = [
    RigaddonAnimationItem,
    RigaddonAnimationProperties,
    RigaddonPresetProperties,
]

def register():
    """Register property classes"""
    from bpy.utils import register_class
    
    for cls in rigaddon_property_classes:
        register_class(cls)
    
    # Register properties to scene
    bpy.types.Scene.rigaddon_animation_props = bpy.props.PointerProperty(
        type=RigaddonAnimationProperties
    )
    bpy.types.Scene.rigaddon_preset_props = bpy.props.PointerProperty(
        type=RigaddonPresetProperties
    )

def unregister():
    """Unregister property classes"""
    from bpy.utils import unregister_class
    
    # Remove properties from scene
    if hasattr(bpy.types.Scene, "rigaddon_animation_props"):
        del bpy.types.Scene.rigaddon_animation_props
    if hasattr(bpy.types.Scene, "rigaddon_preset_props"):
        del bpy.types.Scene.rigaddon_preset_props
    
    for cls in reversed(rigaddon_property_classes):
        unregister_class(cls)
