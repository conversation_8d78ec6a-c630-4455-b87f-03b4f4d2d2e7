# Rigaddon Animation - Advanced Operators
# Created by: <PERSON><PERSON><PERSON>

import bpy
from bpy.types import Operator
from bpy.props import StringProperty, IntProperty, FloatProperty, BoolProperty, EnumProperty, FloatVectorProperty
from mathutils import Vector

class RIGADDON_OT_BlendAnimations(Operator):
    """Blend two animations together"""
    bl_idname = "rigaddon.blend_animations"
    bl_label = "Blend Animations"
    bl_description = "Blend two animations together with specified factor"
    bl_options = {'REGISTER', 'UNDO'}
    
    rigaddon_blend_factor: FloatProperty(
        name="Blend Factor",
        description="Blend factor between animations (0=first, 1=second)",
        default=0.5,
        min=0.0,
        max=1.0
    )
    
    rigaddon_anim1_start: IntProperty(
        name="Animation 1 Start",
        description="Start frame of first animation",
        default=1
    )
    
    rigaddon_anim1_end: IntProperty(
        name="Animation 1 End",
        description="End frame of first animation",
        default=30
    )
    
    rigaddon_anim2_start: IntProperty(
        name="Animation 2 Start",
        description="Start frame of second animation",
        default=31
    )
    
    rigaddon_anim2_end: IntProperty(
        name="Animation 2 End",
        description="End frame of second animation",
        default=60
    )
    
    @classmethod
    def poll(cls, context):
        return context.active_object is not None
    
    def execute(self, context):
        try:
            from ..utils.advanced_features import RigaddonAdvancedFeatures
            
            obj = context.active_object
            
            success = RigaddonAdvancedFeatures.rigaddon_blend_animations(
                obj, self.rigaddon_blend_factor,
                self.rigaddon_anim1_start, self.rigaddon_anim1_end,
                self.rigaddon_anim2_start, self.rigaddon_anim2_end
            )
            
            if success:
                self.report({'INFO'}, f"Blended animations with factor {self.rigaddon_blend_factor}")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to blend animations")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error blending animations: {str(e)}")
            return {'CANCELLED'}
    
    def invoke(self, context, event):
        return context.window_manager.invoke_props_dialog(self)
    
    def draw(self, context):
        layout = self.layout
        layout.prop(self, "rigaddon_blend_factor")
        layout.separator()
        
        col = layout.column()
        col.label(text="Animation 1:")
        row = col.row(align=True)
        row.prop(self, "rigaddon_anim1_start")
        row.prop(self, "rigaddon_anim1_end")
        
        col.label(text="Animation 2:")
        row = col.row(align=True)
        row.prop(self, "rigaddon_anim2_start")
        row.prop(self, "rigaddon_anim2_end")

class RIGADDON_OT_CreatePathAnimation(Operator):
    """Create animation following a path"""
    bl_idname = "rigaddon.create_path_animation"
    bl_label = "Create Path Animation"
    bl_description = "Create animation that follows a custom path"
    bl_options = {'REGISTER', 'UNDO'}
    
    rigaddon_duration: IntProperty(
        name="Duration",
        description="Duration of path animation in frames",
        default=60,
        min=1
    )
    
    rigaddon_start_frame: IntProperty(
        name="Start Frame",
        description="Starting frame for animation",
        default=1,
        min=1
    )
    
    rigaddon_use_curve: BoolProperty(
        name="Use Selected Curve",
        description="Use selected curve object as path",
        default=True
    )
    
    @classmethod
    def poll(cls, context):
        return context.active_object is not None
    
    def execute(self, context):
        try:
            from ..utils.advanced_features import RigaddonAdvancedFeatures
            
            obj = context.active_object
            path_points = []
            
            if self.rigaddon_use_curve:
                # Find curve object
                curve_obj = None
                for selected_obj in context.selected_objects:
                    if selected_obj.type == 'CURVE' and selected_obj != obj:
                        curve_obj = selected_obj
                        break
                
                if curve_obj:
                    # Extract points from curve
                    curve_data = curve_obj.data
                    if curve_data.splines:
                        spline = curve_data.splines[0]
                        if spline.type == 'BEZIER':
                            for point in spline.bezier_points:
                                world_point = curve_obj.matrix_world @ point.co
                                path_points.append(Vector(world_point))
                        else:
                            for point in spline.points:
                                world_point = curve_obj.matrix_world @ Vector(point.co[:3])
                                path_points.append(world_point)
                else:
                    self.report({'ERROR'}, "No curve object selected")
                    return {'CANCELLED'}
            else:
                # Create simple path (for demo)
                start_loc = Vector(obj.location)
                path_points = [
                    start_loc,
                    start_loc + Vector((2, 0, 1)),
                    start_loc + Vector((4, 2, 0)),
                    start_loc + Vector((6, 0, -1)),
                    start_loc + Vector((8, 0, 0))
                ]
            
            success = RigaddonAdvancedFeatures.rigaddon_create_path_animation(
                obj, path_points, self.rigaddon_duration, self.rigaddon_start_frame
            )
            
            if success:
                self.report({'INFO'}, f"Created path animation with {len(path_points)} points")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to create path animation")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error creating path animation: {str(e)}")
            return {'CANCELLED'}
    
    def invoke(self, context, event):
        return context.window_manager.invoke_props_dialog(self)
    
    def draw(self, context):
        layout = self.layout
        layout.prop(self, "rigaddon_duration")
        layout.prop(self, "rigaddon_start_frame")
        layout.prop(self, "rigaddon_use_curve")

class RIGADDON_OT_CreateNoiseAnimation(Operator):
    """Create noise-based animation"""
    bl_idname = "rigaddon.create_noise_animation"
    bl_label = "Create Noise Animation"
    bl_description = "Create animation with noise for organic movement"
    bl_options = {'REGISTER', 'UNDO'}
    
    rigaddon_property: EnumProperty(
        name="Property",
        description="Property to animate with noise",
        items=[
            ('location', 'Location', 'Animate location'),
            ('rotation', 'Rotation', 'Animate rotation'),
            ('scale', 'Scale', 'Animate scale'),
        ],
        default='location'
    )
    
    rigaddon_intensity: FloatProperty(
        name="Intensity",
        description="Intensity of noise effect",
        default=1.0,
        min=0.0,
        max=10.0
    )
    
    rigaddon_frequency: FloatProperty(
        name="Frequency",
        description="Frequency of noise oscillation",
        default=1.0,
        min=0.1,
        max=10.0
    )
    
    rigaddon_duration: IntProperty(
        name="Duration",
        description="Duration of noise animation in frames",
        default=60,
        min=1
    )
    
    rigaddon_start_frame: IntProperty(
        name="Start Frame",
        description="Starting frame for animation",
        default=1,
        min=1
    )
    
    @classmethod
    def poll(cls, context):
        return context.active_object is not None
    
    def execute(self, context):
        try:
            from ..utils.advanced_features import RigaddonAdvancedFeatures
            
            obj = context.active_object
            
            success = RigaddonAdvancedFeatures.rigaddon_create_noise_animation(
                obj, self.rigaddon_property, self.rigaddon_intensity, 
                self.rigaddon_frequency, self.rigaddon_duration, self.rigaddon_start_frame
            )
            
            if success:
                self.report({'INFO'}, f"Created noise animation for {self.rigaddon_property}")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to create noise animation")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error creating noise animation: {str(e)}")
            return {'CANCELLED'}
    
    def invoke(self, context, event):
        return context.window_manager.invoke_props_dialog(self)
    
    def draw(self, context):
        layout = self.layout
        layout.prop(self, "rigaddon_property")
        layout.prop(self, "rigaddon_intensity")
        layout.prop(self, "rigaddon_frequency")
        layout.prop(self, "rigaddon_duration")
        layout.prop(self, "rigaddon_start_frame")

class RIGADDON_OT_CreatePhysicsAnimation(Operator):
    """Create physics-based animation"""
    bl_idname = "rigaddon.create_physics_animation"
    bl_label = "Create Physics Animation"
    bl_description = "Create animation based on physics simulation"
    bl_options = {'REGISTER', 'UNDO'}
    
    rigaddon_simulation_type: EnumProperty(
        name="Simulation Type",
        description="Type of physics simulation",
        items=[
            ('gravity_fall', 'Gravity Fall', 'Simple gravity fall simulation'),
            ('pendulum', 'Pendulum', 'Pendulum motion simulation'),
        ],
        default='gravity_fall'
    )
    
    rigaddon_duration: IntProperty(
        name="Duration",
        description="Duration of physics animation in frames",
        default=60,
        min=1
    )
    
    rigaddon_start_frame: IntProperty(
        name="Start Frame",
        description="Starting frame for animation",
        default=1,
        min=1
    )
    
    @classmethod
    def poll(cls, context):
        return context.active_object is not None
    
    def execute(self, context):
        try:
            from ..utils.advanced_features import RigaddonAdvancedFeatures
            
            obj = context.active_object
            
            success = RigaddonAdvancedFeatures.rigaddon_create_physics_simulation(
                obj, self.rigaddon_simulation_type, self.rigaddon_duration, self.rigaddon_start_frame
            )
            
            if success:
                self.report({'INFO'}, f"Created {self.rigaddon_simulation_type} physics animation")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to create physics animation")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error creating physics animation: {str(e)}")
            return {'CANCELLED'}
    
    def invoke(self, context, event):
        return context.window_manager.invoke_props_dialog(self)
    
    def draw(self, context):
        layout = self.layout
        layout.prop(self, "rigaddon_simulation_type")
        layout.prop(self, "rigaddon_duration")
        layout.prop(self, "rigaddon_start_frame")

class RIGADDON_OT_BatchApplyAnimation(Operator):
    """Apply animation to multiple objects with offset"""
    bl_idname = "rigaddon.batch_apply_animation"
    bl_label = "Batch Apply Animation"
    bl_description = "Apply animation to multiple selected objects with various offset options"
    bl_options = {'REGISTER', 'UNDO'}
    
    rigaddon_offset_type: EnumProperty(
        name="Offset Type",
        description="Type of offset to apply between objects",
        items=[
            ('time', 'Time Offset', 'Linear time offset'),
            ('random', 'Random Offset', 'Random time offset'),
            ('wave', 'Wave Offset', 'Wave pattern offset'),
            ('none', 'No Offset', 'No offset between objects'),
        ],
        default='time'
    )
    
    rigaddon_offset_value: FloatProperty(
        name="Offset Value",
        description="Offset value in frames",
        default=5.0,
        min=0.0,
        max=100.0
    )
    
    @classmethod
    def poll(cls, context):
        return len(context.selected_objects) > 1
    
    def execute(self, context):
        try:
            from ..utils.advanced_features import RigaddonAdvancedFeatures
            from ..presets.utils_presets import rigaddon_preset_manager
            
            preset_props = context.scene.rigaddon_preset_props
            
            if preset_props.rigaddon_selected_category == 'NONE' or preset_props.rigaddon_selected_preset == 'NONE':
                self.report({'ERROR'}, "No preset selected")
                return {'CANCELLED'}
            
            # Get preset data
            preset_data = rigaddon_preset_manager.get_rigaddon_preset_by_name(
                preset_props.rigaddon_selected_category,
                preset_props.rigaddon_selected_preset
            )
            
            if not preset_data:
                self.report({'ERROR'}, "Preset not found")
                return {'CANCELLED'}
            
            # Apply to selected objects
            selected_objects = context.selected_objects.copy()
            
            applied_count = RigaddonAdvancedFeatures.rigaddon_batch_apply_animation(
                selected_objects, preset_data, self.rigaddon_offset_type, self.rigaddon_offset_value
            )
            
            if applied_count > 0:
                self.report({'INFO'}, f"Applied animation to {applied_count} objects")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to apply animation to any objects")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error in batch apply: {str(e)}")
            return {'CANCELLED'}
    
    def invoke(self, context, event):
        return context.window_manager.invoke_props_dialog(self)
    
    def draw(self, context):
        layout = self.layout
        layout.prop(self, "rigaddon_offset_type")
        layout.prop(self, "rigaddon_offset_value")
