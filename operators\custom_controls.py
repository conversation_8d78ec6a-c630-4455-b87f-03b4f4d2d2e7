# Rigaddon Animation - Custom Controls Operators
# Created by: <PERSON><PERSON><PERSON>

import bpy
from bpy.types import Operator
from bpy.props import StringProperty, IntProperty, FloatProperty, BoolProperty, EnumProperty

class RIGADDON_OT_CustomizeAnimation(Operator):
    """Open animation customization panel"""
    bl_idname = "rigaddon.customize_animation"
    bl_label = "Customize Animation"
    bl_description = "Open customization panel for selected animation"
    bl_options = {'REGISTER', 'UNDO'}
    
    rigaddon_animation_index: IntProperty(
        name="Animation Index",
        description="Index of animation to customize",
        default=-1
    )
    
    @classmethod
    def poll(cls, context):
        anim_props = context.scene.rigaddon_animation_props
        return len(anim_props.rigaddon_animation_list) > 0
    
    def execute(self, context):
        # This operator mainly toggles the advanced view
        anim_props = context.scene.rigaddon_animation_props
        anim_props.rigaddon_show_advanced = not anim_props.rigaddon_show_advanced
        
        if anim_props.rigaddon_show_advanced:
            self.report({'INFO'}, "Advanced controls enabled")
        else:
            self.report({'INFO'}, "Advanced controls disabled")
        
        return {'FINISHED'}

class RIGADDON_OT_UpdateAnimationProperty(Operator):
    """Update animation property and apply to object"""
    bl_idname = "rigaddon.update_animation_property"
    bl_label = "Update Animation Property"
    bl_description = "Update animation property and apply changes to object"
    bl_options = {'REGISTER', 'UNDO'}
    
    rigaddon_animation_index: IntProperty(
        name="Animation Index",
        description="Index of animation to update",
        default=-1
    )
    
    rigaddon_property_name: StringProperty(
        name="Property Name",
        description="Name of the property to update",
        default=""
    )
    
    rigaddon_apply_changes: BoolProperty(
        name="Apply Changes",
        description="Apply changes to the object immediately",
        default=True
    )
    
    @classmethod
    def poll(cls, context):
        anim_props = context.scene.rigaddon_animation_props
        return len(anim_props.rigaddon_animation_list) > 0
    
    def execute(self, context):
        try:
            from ..utils.animation_core import RigaddonAnimationCore
            from ..presets.utils_presets import rigaddon_preset_manager
            
            anim_props = context.scene.rigaddon_animation_props
            
            # Get animation index
            if self.rigaddon_animation_index >= 0:
                anim_index = self.rigaddon_animation_index
            else:
                anim_index = anim_props.rigaddon_animation_list_index
            
            # Validate index
            if anim_index >= len(anim_props.rigaddon_animation_list):
                self.report({'ERROR'}, "Invalid animation index")
                return {'CANCELLED'}
            
            animation_item = anim_props.rigaddon_animation_list[anim_index]
            
            if self.rigaddon_apply_changes:
                # Get target object
                target_obj = bpy.data.objects.get(animation_item.rigaddon_anim_target_object)
                if not target_obj:
                    self.report({'ERROR'}, f"Target object '{animation_item.rigaddon_anim_target_object}' not found")
                    return {'CANCELLED'}
                
                # Get original preset data
                preset_data = rigaddon_preset_manager.get_rigaddon_preset_by_name(
                    animation_item.rigaddon_anim_preset_category,
                    animation_item.rigaddon_anim_preset_name
                )
                
                if preset_data:
                    # Apply modified preset
                    success = RigaddonAnimationCore.rigaddon_apply_preset_to_object(
                        target_obj, preset_data, 
                        animation_item.rigaddon_anim_start_frame,
                        animation_item.rigaddon_anim_duration
                    )
                    
                    if success:
                        # Apply speed multiplier if different from 1.0
                        if animation_item.rigaddon_anim_speed_multiplier != 1.0:
                            self.rigaddon_apply_speed_multiplier(target_obj, animation_item.rigaddon_anim_speed_multiplier)
                        
                        self.report({'INFO'}, f"Updated animation '{animation_item.rigaddon_anim_name}'")
                    else:
                        self.report({'ERROR'}, "Failed to apply animation changes")
                        return {'CANCELLED'}
                else:
                    self.report({'ERROR'}, "Original preset not found")
                    return {'CANCELLED'}
            
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Error updating animation: {str(e)}")
            return {'CANCELLED'}
    
    def rigaddon_apply_speed_multiplier(self, obj: bpy.types.Object, speed_multiplier: float):
        """Apply speed multiplier to object animation"""
        try:
            if not obj.animation_data or not obj.animation_data.action:
                return
            
            action = obj.animation_data.action
            
            # Scale time for all keyframes
            for fcurve in action.fcurves:
                for keyframe in fcurve.keyframe_points:
                    # Scale the frame position
                    keyframe.co[0] /= speed_multiplier
                    keyframe.handle_left[0] /= speed_multiplier
                    keyframe.handle_right[0] /= speed_multiplier
            
        except Exception as e:
            print(f"Rigaddon Animation: Error applying speed multiplier - {e}")

class RIGADDON_OT_ScaleAnimation(Operator):
    """Scale animation values"""
    bl_idname = "rigaddon.scale_animation"
    bl_label = "Scale Animation"
    bl_description = "Scale animation values by a factor"
    bl_options = {'REGISTER', 'UNDO'}
    
    rigaddon_animation_index: IntProperty(
        name="Animation Index",
        description="Index of animation to scale",
        default=-1
    )
    
    rigaddon_scale_factor: FloatProperty(
        name="Scale Factor",
        description="Factor to scale animation values",
        default=1.0,
        min=0.1,
        max=10.0
    )
    
    @classmethod
    def poll(cls, context):
        anim_props = context.scene.rigaddon_animation_props
        return len(anim_props.rigaddon_animation_list) > 0
    
    def execute(self, context):
        try:
            from ..utils.animation_core import RigaddonAnimationCore
            
            anim_props = context.scene.rigaddon_animation_props
            
            # Get animation index
            if self.rigaddon_animation_index >= 0:
                anim_index = self.rigaddon_animation_index
            else:
                anim_index = anim_props.rigaddon_animation_list_index
            
            # Validate index
            if anim_index >= len(anim_props.rigaddon_animation_list):
                self.report({'ERROR'}, "Invalid animation index")
                return {'CANCELLED'}
            
            animation_item = anim_props.rigaddon_animation_list[anim_index]
            
            # Get target object
            target_obj = bpy.data.objects.get(animation_item.rigaddon_anim_target_object)
            if not target_obj:
                self.report({'ERROR'}, f"Target object '{animation_item.rigaddon_anim_target_object}' not found")
                return {'CANCELLED'}
            
            # Scale animation
            success = RigaddonAnimationCore.rigaddon_scale_animation(target_obj, self.rigaddon_scale_factor)
            
            if success:
                self.report({'INFO'}, f"Scaled animation by factor {self.rigaddon_scale_factor}")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to scale animation")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error scaling animation: {str(e)}")
            return {'CANCELLED'}
    
    def invoke(self, context, event):
        return context.window_manager.invoke_props_dialog(self)
    
    def draw(self, context):
        layout = self.layout
        layout.prop(self, "rigaddon_scale_factor")

class RIGADDON_OT_ReverseAnimation(Operator):
    """Reverse animation direction"""
    bl_idname = "rigaddon.reverse_animation"
    bl_label = "Reverse Animation"
    bl_description = "Reverse the direction of the animation"
    bl_options = {'REGISTER', 'UNDO'}
    
    rigaddon_animation_index: IntProperty(
        name="Animation Index",
        description="Index of animation to reverse",
        default=-1
    )
    
    @classmethod
    def poll(cls, context):
        anim_props = context.scene.rigaddon_animation_props
        return len(anim_props.rigaddon_animation_list) > 0
    
    def execute(self, context):
        try:
            from ..utils.animation_core import RigaddonAnimationCore
            
            anim_props = context.scene.rigaddon_animation_props
            
            # Get animation index
            if self.rigaddon_animation_index >= 0:
                anim_index = self.rigaddon_animation_index
            else:
                anim_index = anim_props.rigaddon_animation_list_index
            
            # Validate index
            if anim_index >= len(anim_props.rigaddon_animation_list):
                self.report({'ERROR'}, "Invalid animation index")
                return {'CANCELLED'}
            
            animation_item = anim_props.rigaddon_animation_list[anim_index]
            
            # Get target object
            target_obj = bpy.data.objects.get(animation_item.rigaddon_anim_target_object)
            if not target_obj:
                self.report({'ERROR'}, f"Target object '{animation_item.rigaddon_anim_target_object}' not found")
                return {'CANCELLED'}
            
            # Reverse animation
            success = RigaddonAnimationCore.rigaddon_reverse_animation(
                target_obj, 
                animation_item.rigaddon_anim_start_frame,
                animation_item.rigaddon_anim_end_frame
            )
            
            if success:
                self.report({'INFO'}, f"Reversed animation '{animation_item.rigaddon_anim_name}'")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to reverse animation")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error reversing animation: {str(e)}")
            return {'CANCELLED'}

class RIGADDON_OT_ToggleAnimationEnabled(Operator):
    """Toggle animation enabled state"""
    bl_idname = "rigaddon.toggle_animation_enabled"
    bl_label = "Toggle Animation"
    bl_description = "Toggle animation enabled/disabled state"
    bl_options = {'REGISTER', 'UNDO'}
    
    rigaddon_animation_index: IntProperty(
        name="Animation Index",
        description="Index of animation to toggle",
        default=-1
    )
    
    @classmethod
    def poll(cls, context):
        anim_props = context.scene.rigaddon_animation_props
        return len(anim_props.rigaddon_animation_list) > 0
    
    def execute(self, context):
        try:
            anim_props = context.scene.rigaddon_animation_props
            
            # Get animation index
            if self.rigaddon_animation_index >= 0:
                anim_index = self.rigaddon_animation_index
            else:
                anim_index = anim_props.rigaddon_animation_list_index
            
            # Validate index
            if anim_index >= len(anim_props.rigaddon_animation_list):
                self.report({'ERROR'}, "Invalid animation index")
                return {'CANCELLED'}
            
            animation_item = anim_props.rigaddon_animation_list[anim_index]
            
            # Toggle enabled state
            animation_item.rigaddon_anim_enabled = not animation_item.rigaddon_anim_enabled
            
            status = "enabled" if animation_item.rigaddon_anim_enabled else "disabled"
            self.report({'INFO'}, f"Animation '{animation_item.rigaddon_anim_name}' {status}")
            
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Error toggling animation: {str(e)}")
            return {'CANCELLED'}
