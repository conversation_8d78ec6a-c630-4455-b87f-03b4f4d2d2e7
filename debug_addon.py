# Rigaddon Animation - Debug Script
# Run this in Blender's Text Editor to diagnose addon issues

import bpy
import sys
import os

def debug_addon_installation():
    """Debug addon installation issues"""
    print("=" * 60)
    print("RIGADDON ANIMATION - DIAGNOSTIC SCRIPT")
    print("=" * 60)
    
    # Check if addon is in addons list
    print("\n1. CHECKING ADDON REGISTRATION:")
    addon_found = False
    for addon in bpy.context.preferences.addons:
        if "rigaddon" in addon.module.lower():
            print(f"✓ Found addon: {addon.module}")
            addon_found = True
            break
    
    if not addon_found:
        print("✗ Rigaddon Animation addon not found in enabled addons")
        print("  Please enable the addon in Preferences > Add-ons")
        return False
    
    # Check operators
    print("\n2. CHECKING OPERATORS:")
    test_operators = [
        "rigaddon.add_animation",
        "rigaddon.play_animation", 
        "rigaddon.remove_animation"
    ]
    
    for op_name in test_operators:
        try:
            module_name, op_name_only = op_name.split('.')
            if hasattr(bpy.ops, module_name):
                module = getattr(bpy.ops, module_name)
                if hasattr(module, op_name_only):
                    print(f"✓ Operator {op_name} found")
                else:
                    print(f"✗ Operator {op_name} not found")
            else:
                print(f"✗ Module {module_name} not found")
        except Exception as e:
            print(f"✗ Error checking operator {op_name}: {e}")
    
    # Check properties
    print("\n3. CHECKING PROPERTIES:")
    scene = bpy.context.scene
    if hasattr(scene, 'rigaddon_animation_props'):
        print("✓ Animation properties found")
    else:
        print("✗ Animation properties not found")
    
    if hasattr(scene, 'rigaddon_preset_props'):
        print("✓ Preset properties found")
    else:
        print("✗ Preset properties not found")
    
    # Check panels
    print("\n4. CHECKING UI PANELS:")
    panel_classes = [
        "RIGADDON_PT_animation_main",
        "RIGADDON_PT_animation_presets"
    ]
    
    for panel_name in panel_classes:
        if hasattr(bpy.types, panel_name):
            print(f"✓ Panel {panel_name} registered")
        else:
            print(f"✗ Panel {panel_name} not registered")
    
    # Check if sidebar category exists
    print("\n5. CHECKING SIDEBAR CATEGORY:")
    try:
        # This is a bit tricky to check directly, but we can try to access the space
        for area in bpy.context.screen.areas:
            if area.type == 'VIEW_3D':
                for space in area.spaces:
                    if space.type == 'VIEW_3D':
                        print(f"✓ 3D Viewport found")
                        break
                break
    except Exception as e:
        print(f"✗ Error checking 3D Viewport: {e}")
    
    # Check console for errors
    print("\n6. CHECKING FOR COMMON ISSUES:")
    
    # Check Blender version
    blender_version = bpy.app.version
    if blender_version >= (4, 3, 0):
        print(f"✓ Blender version {blender_version} is compatible")
    else:
        print(f"✗ Blender version {blender_version} may not be compatible (need 4.3+)")
    
    print("\n" + "=" * 60)
    print("DIAGNOSTIC COMPLETE")
    print("=" * 60)
    
    return True

def force_refresh_ui():
    """Force refresh the UI"""
    print("\nForcing UI refresh...")
    try:
        # Refresh all areas
        for area in bpy.context.screen.areas:
            area.tag_redraw()
        
        # Toggle sidebar to refresh
        for area in bpy.context.screen.areas:
            if area.type == 'VIEW_3D':
                for space in area.spaces:
                    if space.type == 'VIEW_3D':
                        # Toggle sidebar
                        with bpy.context.temp_override(area=area, space=space):
                            bpy.ops.view3d.properties()
                            bpy.ops.view3d.properties()
                        break
                break
        
        print("✓ UI refresh completed")
        
    except Exception as e:
        print(f"✗ Error refreshing UI: {e}")

def check_addon_files():
    """Check if all addon files exist"""
    print("\n7. CHECKING ADDON FILES:")
    
    # Get addon path
    addon_paths = bpy.utils.script_paths("addons")
    rigaddon_path = None
    
    for path in addon_paths:
        potential_path = os.path.join(path, "Rigaddon Animation")
        if os.path.exists(potential_path):
            rigaddon_path = potential_path
            break
    
    if not rigaddon_path:
        print("✗ Rigaddon Animation folder not found in addons directory")
        print("  Expected locations:")
        for path in addon_paths:
            print(f"    {os.path.join(path, 'Rigaddon Animation')}")
        return False
    
    print(f"✓ Addon found at: {rigaddon_path}")
    
    # Check required files
    required_files = [
        "__init__.py",
        "ui/__init__.py",
        "ui/panels.py",
        "operators/__init__.py",
        "properties/__init__.py",
        "presets/anim_presets.json"
    ]
    
    for file_path in required_files:
        full_path = os.path.join(rigaddon_path, file_path)
        if os.path.exists(full_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} missing")
    
    return True

def manual_register_test():
    """Try to manually register the addon"""
    print("\n8. MANUAL REGISTRATION TEST:")
    
    try:
        # Try to import the addon
        import importlib
        
        # Remove from cache if exists
        addon_modules = [name for name in sys.modules.keys() if 'rigaddon' in name.lower()]
        for module_name in addon_modules:
            if module_name in sys.modules:
                del sys.modules[module_name]
        
        # Try to import
        addon_name = None
        for addon in bpy.context.preferences.addons:
            if "rigaddon" in addon.module.lower():
                addon_name = addon.module
                break
        
        if addon_name:
            print(f"Attempting to reload addon: {addon_name}")
            
            # Disable and re-enable
            bpy.ops.preferences.addon_disable(module=addon_name)
            bpy.ops.preferences.addon_enable(module=addon_name)
            
            print("✓ Addon reloaded successfully")
        else:
            print("✗ Could not find addon to reload")
            
    except Exception as e:
        print(f"✗ Manual registration failed: {e}")

# Run all diagnostics
if __name__ == "__main__":
    debug_addon_installation()
    check_addon_files()
    manual_register_test()
    force_refresh_ui()
    
    print("\n" + "=" * 60)
    print("TROUBLESHOOTING TIPS:")
    print("1. Make sure addon is enabled in Preferences > Add-ons")
    print("2. Search for 'Rigaddon' in the addon list")
    print("3. Check the 3D Viewport sidebar (press N to toggle)")
    print("4. Look for 'Rigaddon' tab in the sidebar")
    print("5. Try restarting Blender if issues persist")
    print("=" * 60)
