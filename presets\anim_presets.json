{"animation_presets": {"basic_movements": {"bounce": {"name": "<PERSON><PERSON><PERSON>", "description": "Simple bounce animation", "duration": 60, "properties": {"location": {"keyframes": [{"frame": 1, "value": [0, 0, 0], "interpolation": "BEZIER"}, {"frame": 15, "value": [0, 0, 2], "interpolation": "BEZIER"}, {"frame": 30, "value": [0, 0, 0], "interpolation": "BEZIER"}, {"frame": 45, "value": [0, 0, 1], "interpolation": "BEZIER"}, {"frame": 60, "value": [0, 0, 0], "interpolation": "BEZIER"}]}}, "easing": "ease_out", "loop": true}, "slide_in": {"name": "Slide In", "description": "Object slides in from the side", "duration": 30, "properties": {"location": {"keyframes": [{"frame": 1, "value": [-5, 0, 0], "interpolation": "BEZIER"}, {"frame": 30, "value": [0, 0, 0], "interpolation": "BEZIER"}]}}, "easing": "ease_in_out", "loop": false}, "fade_in": {"name": "Fade In", "description": "Object fades in gradually", "duration": 24, "properties": {"alpha": {"keyframes": [{"frame": 1, "value": 0.0, "interpolation": "LINEAR"}, {"frame": 24, "value": 1.0, "interpolation": "LINEAR"}]}}, "easing": "linear", "loop": false}}, "rotations": {"spin": {"name": "Spin", "description": "Continuous spinning animation", "duration": 120, "properties": {"rotation_euler": {"keyframes": [{"frame": 1, "value": [0, 0, 0], "interpolation": "LINEAR"}, {"frame": 120, "value": [0, 0, 6.28318], "interpolation": "LINEAR"}]}}, "easing": "linear", "loop": true}, "wobble": {"name": "Wobble", "description": "Wobbling rotation effect", "duration": 40, "properties": {"rotation_euler": {"keyframes": [{"frame": 1, "value": [0, 0, 0], "interpolation": "BEZIER"}, {"frame": 10, "value": [0, 0, 0.2], "interpolation": "BEZIER"}, {"frame": 20, "value": [0, 0, -0.2], "interpolation": "BEZIER"}, {"frame": 30, "value": [0, 0, 0.1], "interpolation": "BEZIER"}, {"frame": 40, "value": [0, 0, 0], "interpolation": "BEZIER"}]}}, "easing": "ease_out", "loop": true}}, "scaling": {"pulse": {"name": "Pulse", "description": "Pulsing scale animation", "duration": 60, "properties": {"scale": {"keyframes": [{"frame": 1, "value": [1, 1, 1], "interpolation": "BEZIER"}, {"frame": 30, "value": [1.2, 1.2, 1.2], "interpolation": "BEZIER"}, {"frame": 60, "value": [1, 1, 1], "interpolation": "BEZIER"}]}}, "easing": "ease_in_out", "loop": true}, "grow": {"name": "Grow", "description": "Object grows from small to normal size", "duration": 30, "properties": {"scale": {"keyframes": [{"frame": 1, "value": [0.1, 0.1, 0.1], "interpolation": "BEZIER"}, {"frame": 30, "value": [1, 1, 1], "interpolation": "BEZIER"}]}}, "easing": "ease_out", "loop": false}}, "complex": {"float": {"name": "Float", "description": "Gentle floating motion", "duration": 120, "properties": {"location": {"keyframes": [{"frame": 1, "value": [0, 0, 0], "interpolation": "BEZIER"}, {"frame": 60, "value": [0, 0, 0.5], "interpolation": "BEZIER"}, {"frame": 120, "value": [0, 0, 0], "interpolation": "BEZIER"}]}, "rotation_euler": {"keyframes": [{"frame": 1, "value": [0, 0, 0], "interpolation": "BEZIER"}, {"frame": 60, "value": [0, 0, 0.1], "interpolation": "BEZIER"}, {"frame": 120, "value": [0, 0, 0], "interpolation": "BEZIER"}]}}, "easing": "ease_in_out", "loop": true}}}, "easing_curves": {"linear": {"type": "LINEAR"}, "ease_in": {"type": "BEZIER", "handles": "AUTO"}, "ease_out": {"type": "BEZIER", "handles": "AUTO"}, "ease_in_out": {"type": "BEZIER", "handles": "AUTO"}, "bounce": {"type": "BACK"}, "elastic": {"type": "ELASTIC"}}, "default_settings": {"frame_rate": 24, "default_duration": 60, "auto_keyframe": true, "use_fake_user": true}}