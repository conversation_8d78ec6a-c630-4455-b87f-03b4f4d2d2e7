# Rigaddon Animation - Installation Helper
# Run this script in Blender to help with installation issues

import bpy
import os
import sys

def check_addon_installation():
    """Check if addon is properly installed"""
    print("=" * 50)
    print("RIGADDON ANIMATION - INSTALLATION CHECKER")
    print("=" * 50)
    
    # 1. Check if addon is in the addons list
    print("\n1. Checking addon in preferences...")
    addon_found = False
    addon_enabled = False
    
    for addon in bpy.context.preferences.addons:
        if "rigaddon" in addon.module.lower():
            print(f"✓ Found addon module: {addon.module}")
            addon_found = True
            addon_enabled = True
            break
    
    # Check all available addons (including disabled ones)
    if not addon_found:
        print("Checking all available addons...")
        import addon_utils
        
        for mod in addon_utils.modules():
            if "rigaddon" in mod.__name__.lower():
                print(f"✓ Found addon module (disabled): {mod.__name__}")
                addon_found = True
                break
    
    if not addon_found:
        print("✗ Addon not found in Blender")
        print("\nTROUBLESHOOTING:")
        print("1. Make sure you installed the addon correctly")
        print("2. Check the addon folder name is 'Rigaddon Animation'")
        print("3. Restart Blender and try again")
        return False
    
    # 2. Try to enable the addon if it's disabled
    if addon_found and not addon_enabled:
        print("\n2. Attempting to enable addon...")
        try:
            # Find the exact module name
            import addon_utils
            for mod in addon_utils.modules():
                if "rigaddon" in mod.__name__.lower():
                    bpy.ops.preferences.addon_enable(module=mod.__name__)
                    print(f"✓ Enabled addon: {mod.__name__}")
                    addon_enabled = True
                    break
        except Exception as e:
            print(f"✗ Failed to enable addon: {e}")
    
    # 3. Check UI registration
    print("\n3. Checking UI registration...")
    
    # Check if test panel exists
    if hasattr(bpy.types, 'RIGADDON_PT_TestPanel'):
        print("✓ Test panel registered")
    else:
        print("✗ Test panel not registered")
    
    # Check main panel
    if hasattr(bpy.types, 'RIGADDON_PT_AnimationMainPanel'):
        print("✓ Main panel registered")
    else:
        print("✗ Main panel not registered")
    
    # 4. Force UI refresh
    print("\n4. Refreshing UI...")
    try:
        # Refresh all areas
        for area in bpy.context.screen.areas:
            area.tag_redraw()
        print("✓ UI refreshed")
    except Exception as e:
        print(f"✗ UI refresh failed: {e}")
    
    # 5. Check sidebar
    print("\n5. Checking 3D Viewport sidebar...")
    try:
        for area in bpy.context.screen.areas:
            if area.type == 'VIEW_3D':
                for space in area.spaces:
                    if space.type == 'VIEW_3D':
                        print(f"✓ 3D Viewport found")
                        print(f"  Sidebar visible: {space.show_region_ui}")
                        if not space.show_region_ui:
                            print("  ⚠️  Sidebar is hidden - press N to show it")
                        break
                break
    except Exception as e:
        print(f"✗ Error checking sidebar: {e}")
    
    return addon_enabled

def force_addon_reload():
    """Force reload the addon"""
    print("\n" + "=" * 50)
    print("FORCING ADDON RELOAD")
    print("=" * 50)
    
    try:
        # Find and disable addon
        addon_module = None
        for addon in bpy.context.preferences.addons:
            if "rigaddon" in addon.module.lower():
                addon_module = addon.module
                break
        
        if not addon_module:
            # Try to find in all modules
            import addon_utils
            for mod in addon_utils.modules():
                if "rigaddon" in mod.__name__.lower():
                    addon_module = mod.__name__
                    break
        
        if addon_module:
            print(f"Disabling addon: {addon_module}")
            bpy.ops.preferences.addon_disable(module=addon_module)
            
            print(f"Re-enabling addon: {addon_module}")
            bpy.ops.preferences.addon_enable(module=addon_module)
            
            print("✓ Addon reloaded successfully")
            
            # Force UI refresh
            for area in bpy.context.screen.areas:
                area.tag_redraw()
            
            return True
        else:
            print("✗ Could not find addon to reload")
            return False
            
    except Exception as e:
        print(f"✗ Reload failed: {e}")
        return False

def show_installation_guide():
    """Show step-by-step installation guide"""
    print("\n" + "=" * 50)
    print("INSTALLATION GUIDE")
    print("=" * 50)
    
    print("\nIf the addon is not appearing, follow these steps:")
    print("\n1. VERIFY INSTALLATION:")
    print("   - Go to Edit > Preferences > Add-ons")
    print("   - Search for 'Rigaddon' in the search box")
    print("   - Make sure the checkbox is checked to enable it")
    
    print("\n2. CHECK FOLDER STRUCTURE:")
    print("   - The addon folder should be named 'Rigaddon Animation'")
    print("   - It should contain __init__.py and other folders")
    
    print("\n3. CHECK BLENDER VERSION:")
    print(f"   - Your Blender version: {bpy.app.version}")
    print("   - Required: 4.3.0 or higher")
    
    print("\n4. SHOW SIDEBAR:")
    print("   - In 3D Viewport, press N to toggle sidebar")
    print("   - Look for 'Rigaddon' tab in the sidebar")
    
    print("\n5. RESTART BLENDER:")
    print("   - Close Blender completely")
    print("   - Restart and check again")
    
    print("\n6. MANUAL INSTALLATION:")
    addon_paths = bpy.utils.script_paths("addons")
    print("   - Copy addon folder to one of these locations:")
    for path in addon_paths:
        print(f"     {path}")

def quick_test():
    """Quick test to verify addon is working"""
    print("\n" + "=" * 50)
    print("QUICK FUNCTIONALITY TEST")
    print("=" * 50)
    
    # Test properties
    scene = bpy.context.scene
    if hasattr(scene, 'rigaddon_animation_props'):
        print("✓ Animation properties available")
    else:
        print("✗ Animation properties not available")
    
    if hasattr(scene, 'rigaddon_preset_props'):
        print("✓ Preset properties available")
    else:
        print("✗ Preset properties not available")
    
    # Test operators
    test_ops = ['rigaddon.add_animation', 'rigaddon.play_animation']
    for op in test_ops:
        try:
            module, name = op.split('.')
            if hasattr(bpy.ops, module) and hasattr(getattr(bpy.ops, module), name):
                print(f"✓ Operator {op} available")
            else:
                print(f"✗ Operator {op} not available")
        except:
            print(f"✗ Operator {op} not available")

# Main execution
if __name__ == "__main__":
    success = check_addon_installation()
    
    if not success:
        print("\nAttempting to fix installation...")
        force_addon_reload()
    
    quick_test()
    show_installation_guide()
    
    print("\n" + "=" * 50)
    print("NEXT STEPS:")
    print("1. Check the 3D Viewport sidebar (press N)")
    print("2. Look for 'Rigaddon' tab")
    print("3. If still not visible, restart Blender")
    print("4. Run this script again after restart")
    print("=" * 50)
