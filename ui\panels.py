# Rigaddon Animation - UI Panels
# Created by: <PERSON><PERSON><PERSON>

import bpy
from bpy.types import Panel

class RIGADDON_PT_AnimationMainPanel(Panel):
    """Main animation panel in the sidebar"""
    bl_label = "Rigaddon Animation"
    bl_idname = "RIGADDON_PT_animation_main"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "Rigaddon"
    bl_options = {'DEFAULT_CLOSED'}
    
    def draw(self, context):
        layout = self.layout
        scene = context.scene
        anim_props = scene.rigaddon_animation_props
        
        # Header with global controls
        header = layout.box()
        row = header.row(align=True)
        row.operator("rigaddon.play_all_animations", text="Play All", icon='PLAY')
        row.operator("rigaddon.stop_animation", text="Stop", icon='PAUSE')
        row.operator("rigaddon.toggle_pause", text="", icon='FRAME_PREV')
        
        # Global settings
        col = header.column()
        col.prop(anim_props, "rigaddon_global_speed", text="Speed")
        col.prop(anim_props, "rigaddon_playback_mode", text="Mode")
        
        # Animation list
        list_box = layout.box()
        list_box.label(text="Animations", icon='SEQUENCE')
        
        row = list_box.row()
        row.template_list(
            "RIGADDON_UL_AnimationList", "",
            anim_props, "rigaddon_animation_list",
            anim_props, "rigaddon_animation_list_index",
            rows=4
        )
        
        # List controls
        col = row.column(align=True)
        col.operator("rigaddon.add_animation", text="", icon='ADD')
        col.operator("rigaddon.remove_animation", text="", icon='REMOVE')
        col.separator()
        col.operator("rigaddon.duplicate_animation", text="", icon='DUPLICATE')
        col.operator("rigaddon.clear_all_animations", text="", icon='TRASH')
        
        # Animation controls for selected item
        if anim_props.rigaddon_animation_list and anim_props.rigaddon_animation_list_index < len(anim_props.rigaddon_animation_list):
            selected_anim = anim_props.rigaddon_animation_list[anim_props.rigaddon_animation_list_index]
            
            controls_box = layout.box()
            controls_box.label(text=f"Controls: {selected_anim.rigaddon_anim_name}", icon='SETTINGS')
            
            # Play controls
            row = controls_box.row(align=True)
            row.operator("rigaddon.play_animation", text="Play", icon='PLAY')
            row.operator("rigaddon.play_animation_loop", text="Loop", icon='FILE_REFRESH')
            row.operator("rigaddon.play_from_animation", text="Play From", icon='PLAY_REVERSE')
            
            # Toggle enabled
            row = controls_box.row()
            if selected_anim.rigaddon_anim_enabled:
                row.operator("rigaddon.toggle_animation_enabled", text="Disable", icon='HIDE_ON')
            else:
                row.operator("rigaddon.toggle_animation_enabled", text="Enable", icon='HIDE_OFF')
            
            # Advanced controls toggle
            row.operator("rigaddon.customize_animation", 
                        text="Advanced" if not anim_props.rigaddon_show_advanced else "Simple",
                        icon='PREFERENCES')

class RIGADDON_PT_AnimationPresetsPanel(Panel):
    """Presets panel for adding animations"""
    bl_label = "Animation Presets"
    bl_idname = "RIGADDON_PT_animation_presets"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "Rigaddon"
    bl_parent_id = "RIGADDON_PT_animation_main"
    bl_options = {'DEFAULT_CLOSED'}
    
    def draw(self, context):
        layout = self.layout
        scene = context.scene
        preset_props = scene.rigaddon_preset_props
        
        # Preset selection
        preset_box = layout.box()
        preset_box.label(text="Select Preset", icon='PRESET')
        
        col = preset_box.column()
        col.prop(preset_props, "rigaddon_selected_category", text="Category")
        
        if preset_props.rigaddon_selected_category != 'NONE':
            col.prop(preset_props, "rigaddon_selected_preset", text="Preset")
            
            if preset_props.rigaddon_selected_preset != 'NONE':
                # Preview button
                row = col.row()
                row.prop(preset_props, "rigaddon_preview_enabled", text="Preview")
                
                # Preset customization
                custom_box = layout.box()
                custom_box.label(text="Customize", icon='MODIFIER')
                
                col = custom_box.column()
                col.prop(preset_props, "rigaddon_custom_name", text="Name")
                col.prop(preset_props, "rigaddon_custom_duration", text="Duration")
                
                col.separator()
                col.prop(preset_props, "rigaddon_apply_to_selected", text="Apply to All Selected")
                if preset_props.rigaddon_apply_to_selected:
                    col.prop(preset_props, "rigaddon_offset_frames", text="Frame Offset")
                
                # Add animation button
                add_box = layout.box()
                row = add_box.row()
                row.scale_y = 1.5
                row.operator("rigaddon.add_animation", text="Add Animation", icon='PLUS')

class RIGADDON_PT_AnimationControlPanel(Panel):
    """Advanced control panel for selected animation"""
    bl_label = "Animation Controls"
    bl_idname = "RIGADDON_PT_animation_control"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "Rigaddon"
    bl_parent_id = "RIGADDON_PT_animation_main"
    bl_options = {'DEFAULT_CLOSED'}
    
    @classmethod
    def poll(cls, context):
        anim_props = context.scene.rigaddon_animation_props
        return (anim_props.rigaddon_show_advanced and 
                anim_props.rigaddon_animation_list and 
                anim_props.rigaddon_animation_list_index < len(anim_props.rigaddon_animation_list))
    
    def draw(self, context):
        layout = self.layout
        scene = context.scene
        anim_props = scene.rigaddon_animation_props
        
        if not anim_props.rigaddon_animation_list:
            return
        
        selected_anim = anim_props.rigaddon_animation_list[anim_props.rigaddon_animation_list_index]
        
        # Animation properties
        props_box = layout.box()
        props_box.label(text="Properties", icon='PROPERTIES')
        
        col = props_box.column()
        col.prop(selected_anim, "rigaddon_anim_name", text="Name")
        col.prop(selected_anim, "rigaddon_anim_description", text="Description")
        
        # Timing
        timing_box = layout.box()
        timing_box.label(text="Timing", icon='TIME')
        
        col = timing_box.column()
        row = col.row(align=True)
        row.prop(selected_anim, "rigaddon_anim_start_frame", text="Start")
        row.prop(selected_anim, "rigaddon_anim_end_frame", text="End")
        
        col.prop(selected_anim, "rigaddon_anim_duration", text="Duration")
        col.prop(selected_anim, "rigaddon_anim_speed_multiplier", text="Speed")
        
        # Animation properties
        anim_box = layout.box()
        anim_box.label(text="Animation Properties", icon='KEYFRAME')
        
        col = anim_box.column()
        col.prop(selected_anim, "rigaddon_anim_use_location", text="Location")
        col.prop(selected_anim, "rigaddon_anim_use_rotation", text="Rotation")
        col.prop(selected_anim, "rigaddon_anim_use_scale", text="Scale")
        col.prop(selected_anim, "rigaddon_anim_use_alpha", text="Alpha")
        
        col.separator()
        col.prop(selected_anim, "rigaddon_anim_easing", text="Easing")
        col.prop(selected_anim, "rigaddon_anim_loop", text="Loop")
        
        # Advanced operations
        ops_box = layout.box()
        ops_box.label(text="Operations", icon='MODIFIER')
        
        row = ops_box.row(align=True)
        row.operator("rigaddon.scale_animation", text="Scale", icon='FULLSCREEN_ENTER')
        row.operator("rigaddon.reverse_animation", text="Reverse", icon='ARROW_LEFTRIGHT')
        
        # Update button
        update_box = layout.box()
        row = update_box.row()
        row.scale_y = 1.2
        row.operator("rigaddon.update_animation_property", text="Apply Changes", icon='FILE_REFRESH')

class RIGADDON_PT_AnimationToolsPanel(Panel):
    """Tools panel for advanced features"""
    bl_label = "Animation Tools"
    bl_idname = "RIGADDON_PT_animation_tools"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "Rigaddon"
    bl_parent_id = "RIGADDON_PT_animation_main"
    bl_options = {'DEFAULT_CLOSED'}
    
    def draw(self, context):
        layout = self.layout
        scene = context.scene
        preset_props = scene.rigaddon_preset_props
        
        # Custom preset creation
        create_box = layout.box()
        create_box.label(text="Create Custom Preset", icon='PLUS')
        
        col = create_box.column()
        col.prop(preset_props, "rigaddon_create_mode", text="Enable Creation Mode")
        
        if preset_props.rigaddon_create_mode:
            col.prop(preset_props, "rigaddon_new_category", text="Category")
            col.prop(preset_props, "rigaddon_new_preset_name", text="Name")
            col.prop(preset_props, "rigaddon_new_preset_description", text="Description")
            
            col.separator()
            col.prop(preset_props, "rigaddon_record_keyframes", text="Record from Object")
            if preset_props.rigaddon_record_keyframes:
                row = col.row(align=True)
                row.prop(preset_props, "rigaddon_keyframe_start", text="Start")
                row.prop(preset_props, "rigaddon_keyframe_end", text="End")
            
            col.separator()
            col.operator("rigaddon.create_custom_preset", text="Create Preset", icon='FILE_NEW')
        
        # Preset modification
        modify_box = layout.box()
        modify_box.label(text="Modify Presets", icon='MODIFIER')
        
        col = modify_box.column()
        col.prop(preset_props, "rigaddon_modify_mode", text="Enable Modify Mode")
        
        if preset_props.rigaddon_modify_mode:
            col.prop(preset_props, "rigaddon_scale_factor", text="Scale Factor")
            col.prop(preset_props, "rigaddon_time_scale", text="Time Scale")
            col.prop(preset_props, "rigaddon_reverse_animation", text="Reverse")
            
            col.separator()
            col.label(text="Mirror:")
            row = col.row(align=True)
            row.prop(preset_props, "rigaddon_mirror_x", text="X")
            row.prop(preset_props, "rigaddon_mirror_y", text="Y")
            row.prop(preset_props, "rigaddon_mirror_z", text="Z")
        
        # Backup/Restore
        backup_box = layout.box()
        backup_box.label(text="Backup & Restore", icon='FILE_BACKUP')
        
        col = backup_box.column()
        row = col.row(align=True)
        row.operator("rigaddon.backup_presets", text="Backup", icon='EXPORT')
        row.operator("rigaddon.restore_presets", text="Restore", icon='IMPORT')
        
        col.operator("rigaddon.reload_presets", text="Reload Presets", icon='FILE_REFRESH')

class RIGADDON_PT_AdvancedFeaturesPanel(Panel):
    """Advanced features panel"""
    bl_label = "Advanced Features"
    bl_idname = "RIGADDON_PT_advanced_features"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "Rigaddon"
    bl_parent_id = "RIGADDON_PT_animation_main"
    bl_options = {'DEFAULT_CLOSED'}

    def draw(self, context):
        layout = self.layout

        # Animation blending
        blend_box = layout.box()
        blend_box.label(text="Animation Blending", icon='MODIFIER')
        blend_box.operator("rigaddon.blend_animations", text="Blend Animations", icon='ARROW_LEFTRIGHT')

        # Path animation
        path_box = layout.box()
        path_box.label(text="Path Animation", icon='CURVE_PATH')
        path_box.operator("rigaddon.create_path_animation", text="Create Path Animation", icon='CURVE_BEZCURVE')

        # Procedural animation
        proc_box = layout.box()
        proc_box.label(text="Procedural Animation", icon='FORCE_TURBULENCE')

        col = proc_box.column()
        col.operator("rigaddon.create_noise_animation", text="Noise Animation", icon='FORCE_TURBULENCE')
        col.operator("rigaddon.create_physics_animation", text="Physics Animation", icon='PHYSICS')

        # Batch operations
        batch_box = layout.box()
        batch_box.label(text="Batch Operations", icon='GROUP')
        batch_box.operator("rigaddon.batch_apply_animation", text="Batch Apply", icon='GROUP')
