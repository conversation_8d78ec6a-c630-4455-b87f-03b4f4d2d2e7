# Rigaddon Animation - Preset Properties
# Created by: <PERSON><PERSON><PERSON>

import bpy
from bpy.props import (
    StringProperty, IntProperty, FloatProperty, BoolProperty,
    EnumProperty, CollectionProperty, PointerProperty
)
from bpy.types import PropertyGroup

def rigaddon_get_preset_categories(self, context):
    """Get available preset categories for enum"""
    from ..presets.utils_presets import rigaddon_preset_manager
    
    categories = rigaddon_preset_manager.get_rigaddon_preset_categories()
    items = [('NONE', 'Select Category', 'Select a preset category')]
    
    for category in categories:
        display_name = category.replace('_', ' ').title()
        items.append((category, display_name, f"Presets in {display_name} category"))
    
    return items

def rigaddon_get_presets_in_category(self, context):
    """Get available presets in selected category"""
    from ..presets.utils_presets import rigaddon_preset_manager
    
    items = [('NONE', 'Select Preset', 'Select a preset')]
    
    if self.rigaddon_selected_category != 'NONE':
        presets = rigaddon_preset_manager.get_rigaddon_presets_in_category(self.rigaddon_selected_category)
        
        for preset_name, preset_data in presets.items():
            display_name = preset_data.get('name', preset_name)
            description = preset_data.get('description', '')
            items.append((preset_name, display_name, description))
    
    return items

class RigaddonPresetProperties(PropertyGroup):
    """Properties for managing animation presets"""
    
    rigaddon_selected_category: EnumProperty(
        name="Category",
        description="Selected preset category",
        items=rigaddon_get_preset_categories,
        default=0
    )
    
    rigaddon_selected_preset: EnumProperty(
        name="Preset",
        description="Selected animation preset",
        items=rigaddon_get_presets_in_category,
        default=0
    )
    
    rigaddon_custom_name: StringProperty(
        name="Custom Name",
        description="Custom name for the animation",
        default=""
    )
    
    rigaddon_custom_duration: IntProperty(
        name="Duration",
        description="Custom duration in frames",
        default=60,
        min=1,
        max=1000
    )
    
    rigaddon_apply_to_selected: BoolProperty(
        name="Apply to Selected",
        description="Apply animation to all selected objects",
        default=True
    )
    
    rigaddon_offset_frames: IntProperty(
        name="Frame Offset",
        description="Frame offset between objects when applying to multiple",
        default=5,
        min=0,
        max=100
    )
    
    rigaddon_preview_enabled: BoolProperty(
        name="Preview",
        description="Enable preview of selected preset",
        default=False
    )
    
    # Custom preset creation properties
    rigaddon_create_mode: BoolProperty(
        name="Create Mode",
        description="Enable custom preset creation mode",
        default=False
    )
    
    rigaddon_new_category: StringProperty(
        name="New Category",
        description="Name for new preset category",
        default=""
    )
    
    rigaddon_new_preset_name: StringProperty(
        name="Preset Name",
        description="Name for new preset",
        default=""
    )
    
    rigaddon_new_preset_description: StringProperty(
        name="Description",
        description="Description for new preset",
        default=""
    )
    
    rigaddon_record_keyframes: BoolProperty(
        name="Record Keyframes",
        description="Record current object keyframes as preset",
        default=False
    )
    
    rigaddon_keyframe_start: IntProperty(
        name="Start Frame",
        description="Start frame for keyframe recording",
        default=1,
        min=1
    )
    
    rigaddon_keyframe_end: IntProperty(
        name="End Frame",
        description="End frame for keyframe recording",
        default=60,
        min=1
    )
    
    # Preset modification properties
    rigaddon_modify_mode: BoolProperty(
        name="Modify Mode",
        description="Enable preset modification mode",
        default=False
    )
    
    rigaddon_scale_factor: FloatProperty(
        name="Scale Factor",
        description="Scale factor for animation values",
        default=1.0,
        min=0.1,
        max=10.0
    )
    
    rigaddon_time_scale: FloatProperty(
        name="Time Scale",
        description="Time scale for animation duration",
        default=1.0,
        min=0.1,
        max=5.0
    )
    
    rigaddon_reverse_animation: BoolProperty(
        name="Reverse",
        description="Reverse the animation direction",
        default=False
    )
    
    rigaddon_mirror_x: BoolProperty(
        name="Mirror X",
        description="Mirror animation on X axis",
        default=False
    )
    
    rigaddon_mirror_y: BoolProperty(
        name="Mirror Y",
        description="Mirror animation on Y axis",
        default=False
    )
    
    rigaddon_mirror_z: BoolProperty(
        name="Mirror Z",
        description="Mirror animation on Z axis",
        default=False
    )
