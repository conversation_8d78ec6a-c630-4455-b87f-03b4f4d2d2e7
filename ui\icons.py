# Rigaddon Animation - Icons and UI Resources
# Created by: <PERSON><PERSON><PERSON>

import bpy
import os
from bpy.utils import previews

# Global variable to store icons
rigaddon_icon_collections = {}

def rigaddon_load_icons():
    """Load custom icons for Rigaddon Animation"""
    try:
        # Create new icon collection
        pcoll = previews.new()
        
        # Get addon directory
        addon_dir = os.path.dirname(os.path.dirname(__file__))
        icons_dir = os.path.join(addon_dir, "icons")
        
        # Load default Blender icons as placeholders
        # In a real addon, you would load custom icon files here
        rigaddon_icon_collections["rigaddon_main"] = pcoll
        
        print("Rigaddon Animation: Icons loaded successfully")
        
    except Exception as e:
        print(f"Rigaddon Animation: Error loading icons - {e}")

def rigaddon_unload_icons():
    """Unload custom icons"""
    try:
        for pcoll in rigaddon_icon_collections.values():
            previews.remove(pcoll)
        rigaddon_icon_collections.clear()
        
        print("Rigaddon Animation: Icons unloaded successfully")
        
    except Exception as e:
        print(f"Rigaddon Animation: Error unloading icons - {e}")

def rigaddon_get_icon(icon_name: str):
    """Get icon by name"""
    try:
        if "rigaddon_main" in rigaddon_icon_collections:
            pcoll = rigaddon_icon_collections["rigaddon_main"]
            if icon_name in pcoll:
                return pcoll[icon_name].icon_id
        
        # Return default icon if custom not found
        return 0
        
    except Exception as e:
        print(f"Rigaddon Animation: Error getting icon '{icon_name}' - {e}")
        return 0

# Icon constants for easy access
RIGADDON_ICONS = {
    'MAIN': 'SEQUENCE',
    'PLAY': 'PLAY',
    'PAUSE': 'PAUSE',
    'STOP': 'SNAP_FACE',
    'LOOP': 'FILE_REFRESH',
    'ADD': 'ADD',
    'REMOVE': 'REMOVE',
    'DUPLICATE': 'DUPLICATE',
    'SETTINGS': 'SETTINGS',
    'PRESET': 'PRESET',
    'ANIMATION': 'KEYFRAME',
    'ADVANCED': 'PREFERENCES',
    'TOOLS': 'TOOL_SETTINGS',
    'BLEND': 'ARROW_LEFTRIGHT',
    'PATH': 'CURVE_PATH',
    'NOISE': 'FORCE_TURBULENCE',
    'PHYSICS': 'PHYSICS',
    'BATCH': 'GROUP',
    'BACKUP': 'FILE_BACKUP',
    'RESTORE': 'IMPORT',
    'RELOAD': 'FILE_REFRESH',
    'ENABLED': 'HIDE_OFF',
    'DISABLED': 'HIDE_ON',
    'ERROR': 'ERROR',
    'WARNING': 'ERROR',
    'INFO': 'INFO',
    'SUCCESS': 'CHECKMARK',
}

def rigaddon_get_ui_icon(icon_key: str) -> str:
    """Get UI icon by key"""
    return RIGADDON_ICONS.get(icon_key, 'QUESTION')

# UI Helper functions
class RigaddonUIHelpers:
    """Helper functions for UI consistency"""
    
    @staticmethod
    def rigaddon_draw_header(layout, text: str, icon: str = 'NONE'):
        """Draw consistent header"""
        row = layout.row()
        row.label(text=text, icon=icon)
    
    @staticmethod
    def rigaddon_draw_separator(layout, text: str = ""):
        """Draw separator with optional text"""
        layout.separator()
        if text:
            layout.label(text=text)
    
    @staticmethod
    def rigaddon_draw_button_row(layout, operators: list, align: bool = True):
        """Draw row of buttons"""
        row = layout.row(align=align)
        for op_data in operators:
            if isinstance(op_data, dict):
                op = row.operator(op_data['operator'], text=op_data.get('text', ''), icon=op_data.get('icon', 'NONE'))
                # Set properties if provided
                for prop, value in op_data.get('properties', {}).items():
                    setattr(op, prop, value)
            else:
                row.operator(op_data)
        return row
    
    @staticmethod
    def rigaddon_draw_property_group(layout, obj, properties: list, title: str = ""):
        """Draw group of properties"""
        if title:
            layout.label(text=title)
        
        col = layout.column()
        for prop in properties:
            if isinstance(prop, dict):
                col.prop(obj, prop['name'], text=prop.get('text', ''))
            else:
                col.prop(obj, prop)
        
        return col
    
    @staticmethod
    def rigaddon_draw_info_box(layout, text: str, icon: str = 'INFO'):
        """Draw information box"""
        box = layout.box()
        row = box.row()
        row.label(text=text, icon=icon)
        return box
    
    @staticmethod
    def rigaddon_draw_warning_box(layout, text: str):
        """Draw warning box"""
        return RigaddonUIHelpers.rigaddon_draw_info_box(layout, text, 'ERROR')
    
    @staticmethod
    def rigaddon_draw_success_box(layout, text: str):
        """Draw success box"""
        return RigaddonUIHelpers.rigaddon_draw_info_box(layout, text, 'CHECKMARK')

# Color schemes for UI consistency
RIGADDON_COLORS = {
    'PRIMARY': (0.2, 0.6, 1.0, 1.0),      # Blue
    'SECONDARY': (0.8, 0.4, 0.2, 1.0),    # Orange
    'SUCCESS': (0.2, 0.8, 0.2, 1.0),      # Green
    'WARNING': (1.0, 0.8, 0.2, 1.0),      # Yellow
    'ERROR': (1.0, 0.2, 0.2, 1.0),        # Red
    'NEUTRAL': (0.5, 0.5, 0.5, 1.0),      # Gray
}

def rigaddon_get_color(color_key: str) -> tuple:
    """Get color by key"""
    return RIGADDON_COLORS.get(color_key, RIGADDON_COLORS['NEUTRAL'])

# UI Constants
RIGADDON_UI_CONSTANTS = {
    'PANEL_WIDTH': 300,
    'BUTTON_HEIGHT': 1.5,
    'LIST_ROWS': 4,
    'ICON_SIZE': 'SMALL',
    'SPACING': 2,
}

def rigaddon_get_ui_constant(key: str):
    """Get UI constant by key"""
    return RIGADDON_UI_CONSTANTS.get(key, 1)
