# Rigaddon Animation - Installation Guide

## System Requirements

- **Blender Version**: 4.3.0 or higher
- **Operating System**: Windows, macOS, or Linux
- **Python**: 3.10+ (included with Blender)
- **Memory**: Minimum 4GB RAM recommended
- **Storage**: 50MB free space for addon and presets

## Installation Methods

### Method 1: Standard Blender Installation

1. **Download the Addon**
   - Download the complete "Rigaddon Animation" folder
   - Ensure all files and subdirectories are included

2. **Install in Blender**
   - Open Blender 4.3
   - Go to `Edit > Preferences > Add-ons`
   - Click `Install...` button
   - Navigate to and select the addon folder
   - Click `Install Add-on`

3. **Enable the Addon**
   - Search for "Rigaddon Animation" in the addon list
   - Check the checkbox to enable the addon
   - The addon will appear in the 3D Viewport sidebar under "Rigaddon" tab

### Method 2: Manual Installation

1. **Locate Blender Scripts Folder**
   - Windows: `%APPDATA%\Blender Foundation\Blender\4.3\scripts\addons\`
   - macOS: `~/Library/Application Support/Blender/4.3/scripts/addons/`
   - Linux: `~/.config/blender/4.3/scripts/addons/`

2. **Copy Addon Files**
   - Copy the entire "Rigaddon Animation" folder to the addons directory
   - Ensure all subdirectories and files are copied

3. **Restart Blender**
   - Close and restart Blender
   - Go to `Edit > Preferences > Add-ons`
   - Search for "Rigaddon Animation" and enable it

### Method 3: Development Installation

For developers or advanced users:

1. **Clone or Download**
   - Clone the repository or download the source code
   - Ensure the folder structure is maintained

2. **Symlink (Optional)**
   - Create a symbolic link in Blender's addons directory
   - This allows for easier development and updates

3. **Enable Developer Mode**
   - In Blender Preferences, enable "Developer Extras"
   - This provides additional debugging information

## Verification

### Quick Test
1. Open Blender with a default scene
2. Select the default cube
3. Open the sidebar (N key) and go to "Rigaddon" tab
4. You should see the "Rigaddon Animation" panel

### Full Test
1. Run the included test script:
   ```python
   # In Blender's Text Editor, open and run test_addon.py
   exec(open("path/to/test_addon.py").read())
   ```

2. Check console output for test results

## Troubleshooting

### Common Issues

**Addon Not Appearing**
- Check Blender version compatibility (4.3+)
- Verify all files are in the correct directory
- Restart Blender after installation
- Check console for error messages

**Import Errors**
- Ensure all Python files are present
- Check file permissions
- Verify folder structure is intact

**Preset Loading Issues**
- Check if `presets/anim_presets.json` exists
- Verify JSON file is valid
- Check file encoding (should be UTF-8)

**UI Not Loading**
- Refresh the sidebar (close and reopen)
- Check if other addons are conflicting
- Try disabling other animation addons temporarily

### Error Messages

**"Module not found"**
- Reinstall the addon ensuring all files are copied
- Check Python path in Blender preferences

**"JSON decode error"**
- The preset file may be corrupted
- Restore from backup or reinstall

**"Operator not found"**
- The addon may not be fully registered
- Restart Blender and re-enable the addon

### Getting Help

1. **Check Console Output**
   - Open Blender's console window
   - Look for error messages starting with "Rigaddon Animation:"

2. **Enable Debug Mode**
   - In the addon preferences, enable debug mode
   - This provides more detailed error information

3. **Test Individual Components**
   - Use the test script to identify specific issues
   - Test each feature separately

## Uninstallation

### Standard Removal
1. Go to `Edit > Preferences > Add-ons`
2. Find "Rigaddon Animation" in the list
3. Click the dropdown arrow and select "Remove"
4. Confirm the removal

### Manual Removal
1. Navigate to Blender's addons directory
2. Delete the "Rigaddon Animation" folder
3. Restart Blender

### Clean Removal
To completely remove all traces:
1. Remove the addon folder
2. Delete any backup files created by the addon
3. Clear Blender preferences related to the addon
4. Remove any custom presets you created

## File Structure Verification

Ensure your installation includes these files:

```
Rigaddon Animation/
├── __init__.py
├── config.py
├── test_addon.py
├── README.md
├── INSTALL.md
├── presets/
│   ├── __init__.py
│   ├── anim_presets.json
│   └── utils_presets.py
├── ui/
│   ├── __init__.py
│   ├── panels.py
│   ├── lists.py
│   └── icons.py
├── operators/
│   ├── __init__.py
│   ├── add_animation.py
│   ├── play_animation.py
│   ├── remove_animation.py
│   ├── custom_controls.py
│   ├── preset_operators.py
│   └── advanced_operators.py
├── properties/
│   ├── __init__.py
│   ├── anim_properties.py
│   └── preset_properties.py
├── utils/
│   ├── __init__.py
│   ├── json_loader.py
│   ├── animation_core.py
│   ├── playback.py
│   └── advanced_features.py
└── menus/
    ├── __init__.py
    └── main_menu.py
```

## Performance Optimization

### For Better Performance
- Close unnecessary panels when not in use
- Limit the number of simultaneous animations
- Use lower frame rates for complex scenes
- Enable auto-cleanup in addon preferences

### Memory Management
- The addon automatically manages memory usage
- Large animation lists are paginated
- Temporary data is cleaned up automatically

## Updates

### Checking for Updates
- Check the addon source for new versions
- Compare version numbers in the addon preferences

### Updating
1. Disable the current addon
2. Remove the old version
3. Install the new version
4. Re-enable the addon
5. Check that your custom presets are preserved

## Support

For installation issues:
1. Check this guide first
2. Run the test script to identify problems
3. Check Blender's console for error messages
4. Verify system requirements are met

---

**Rigaddon Animation** - Professional animation tools for Blender 4.3
