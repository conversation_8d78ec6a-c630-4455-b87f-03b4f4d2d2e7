# Rigaddon Animation - Preset Operators
# Created by: <PERSON><PERSON><PERSON>

import bpy
from bpy.types import Operator
from bpy.props import StringProperty, IntProperty, BoolProperty, EnumProperty
import os

class RIGADDON_OT_CreateCustomPreset(Operator):
    """Create custom animation preset"""
    bl_idname = "rigaddon.create_custom_preset"
    bl_label = "Create Custom Preset"
    bl_description = "Create a custom animation preset from current object"
    bl_options = {'REGISTER', 'UNDO'}
    
    @classmethod
    def poll(cls, context):
        return context.active_object is not None
    
    def execute(self, context):
        try:
            from ..presets.utils_presets import rigaddon_preset_manager
            from ..utils.json_loader import RigaddonJSONLoader
            
            preset_props = context.scene.rigaddon_preset_props
            
            # Validate input
            if not preset_props.rigaddon_new_preset_name:
                self.report({'ERROR'}, "Preset name is required")
                return {'CANCELLED'}
            
            if not preset_props.rigaddon_new_category:
                self.report({'ERROR'}, "Category name is required")
                return {'CANCELLED'}
            
            # Get active object
            obj = context.active_object
            
            # Export animation data
            start_frame = preset_props.rigaddon_keyframe_start
            end_frame = preset_props.rigaddon_keyframe_end
            
            preset_data = RigaddonJSONLoader.rigaddon_export_animation_to_json(
                obj, start_frame, end_frame,
                preset_props.rigaddon_new_preset_name,
                preset_props.rigaddon_new_preset_description
            )
            
            if not preset_data:
                self.report({'ERROR'}, "Failed to export animation data")
                return {'CANCELLED'}
            
            # Add to preset manager
            success = rigaddon_preset_manager.add_rigaddon_custom_preset(
                preset_props.rigaddon_new_category,
                preset_props.rigaddon_new_preset_name,
                preset_data
            )
            
            if success:
                self.report({'INFO'}, f"Created preset '{preset_props.rigaddon_new_preset_name}' in category '{preset_props.rigaddon_new_category}'")
                
                # Clear input fields
                preset_props.rigaddon_new_preset_name = ""
                preset_props.rigaddon_new_preset_description = ""
                preset_props.rigaddon_create_mode = False
                
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to save custom preset")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error creating preset: {str(e)}")
            return {'CANCELLED'}

class RIGADDON_OT_ReloadPresets(Operator):
    """Reload animation presets from file"""
    bl_idname = "rigaddon.reload_presets"
    bl_label = "Reload Presets"
    bl_description = "Reload animation presets from JSON file"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            from ..presets.utils_presets import rigaddon_preset_manager
            
            success = rigaddon_preset_manager.reload_rigaddon_presets()
            
            if success:
                self.report({'INFO'}, "Presets reloaded successfully")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to reload presets")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error reloading presets: {str(e)}")
            return {'CANCELLED'}

class RIGADDON_OT_BackupPresets(Operator):
    """Backup animation presets"""
    bl_idname = "rigaddon.backup_presets"
    bl_label = "Backup Presets"
    bl_description = "Create backup of animation presets"
    bl_options = {'REGISTER'}
    
    rigaddon_backup_path: StringProperty(
        name="Backup Path",
        description="Path to save backup file",
        default="",
        subtype='FILE_PATH'
    )
    
    def execute(self, context):
        try:
            from ..utils.json_loader import RigaddonJSONLoader
            
            if not self.rigaddon_backup_path:
                # Generate default backup path
                import time
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                addon_dir = os.path.dirname(os.path.dirname(__file__))
                self.rigaddon_backup_path = os.path.join(addon_dir, f"rigaddon_backup_{timestamp}.json")
            
            success = RigaddonJSONLoader.rigaddon_backup_presets(self.rigaddon_backup_path)
            
            if success:
                self.report({'INFO'}, f"Backup saved to: {self.rigaddon_backup_path}")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to create backup")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error creating backup: {str(e)}")
            return {'CANCELLED'}
    
    def invoke(self, context, event):
        context.window_manager.fileselect_add(self)
        return {'RUNNING_MODAL'}

class RIGADDON_OT_RestorePresets(Operator):
    """Restore animation presets from backup"""
    bl_idname = "rigaddon.restore_presets"
    bl_label = "Restore Presets"
    bl_description = "Restore animation presets from backup file"
    bl_options = {'REGISTER', 'UNDO'}
    
    rigaddon_backup_path: StringProperty(
        name="Backup File",
        description="Path to backup file",
        default="",
        subtype='FILE_PATH'
    )
    
    def execute(self, context):
        try:
            from ..utils.json_loader import RigaddonJSONLoader
            
            if not self.rigaddon_backup_path:
                self.report({'ERROR'}, "No backup file selected")
                return {'CANCELLED'}
            
            if not os.path.exists(self.rigaddon_backup_path):
                self.report({'ERROR'}, "Backup file not found")
                return {'CANCELLED'}
            
            success = RigaddonJSONLoader.rigaddon_restore_presets(self.rigaddon_backup_path)
            
            if success:
                self.report({'INFO'}, f"Presets restored from: {self.rigaddon_backup_path}")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to restore presets")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error restoring presets: {str(e)}")
            return {'CANCELLED'}
    
    def invoke(self, context, event):
        context.window_manager.fileselect_add(self)
        return {'RUNNING_MODAL'}

class RIGADDON_OT_DeleteCustomPreset(Operator):
    """Delete custom animation preset"""
    bl_idname = "rigaddon.delete_custom_preset"
    bl_label = "Delete Custom Preset"
    bl_description = "Delete a custom animation preset"
    bl_options = {'REGISTER', 'UNDO'}
    
    rigaddon_category: StringProperty(
        name="Category",
        description="Preset category",
        default=""
    )
    
    rigaddon_preset_name: StringProperty(
        name="Preset Name",
        description="Name of preset to delete",
        default=""
    )
    
    def execute(self, context):
        try:
            from ..presets.utils_presets import rigaddon_preset_manager
            
            if not self.rigaddon_category or not self.rigaddon_preset_name:
                self.report({'ERROR'}, "Category and preset name are required")
                return {'CANCELLED'}
            
            success = rigaddon_preset_manager.remove_rigaddon_custom_preset(
                self.rigaddon_category, self.rigaddon_preset_name
            )
            
            if success:
                self.report({'INFO'}, f"Deleted preset '{self.rigaddon_preset_name}' from category '{self.rigaddon_category}'")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to delete preset")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error deleting preset: {str(e)}")
            return {'CANCELLED'}
    
    def invoke(self, context, event):
        return context.window_manager.invoke_confirm(self, event)

class RIGADDON_OT_PreviewPreset(Operator):
    """Preview animation preset"""
    bl_idname = "rigaddon.preview_preset"
    bl_label = "Preview Preset"
    bl_description = "Preview the selected animation preset"
    bl_options = {'REGISTER'}
    
    @classmethod
    def poll(cls, context):
        return context.active_object is not None
    
    def execute(self, context):
        try:
            from ..presets.utils_presets import rigaddon_preset_manager
            from ..utils.animation_core import RigaddonAnimationCore
            
            preset_props = context.scene.rigaddon_preset_props
            
            if preset_props.rigaddon_selected_category == 'NONE' or preset_props.rigaddon_selected_preset == 'NONE':
                self.report({'ERROR'}, "No preset selected")
                return {'CANCELLED'}
            
            # Get preset data
            preset_data = rigaddon_preset_manager.get_rigaddon_preset_by_name(
                preset_props.rigaddon_selected_category,
                preset_props.rigaddon_selected_preset
            )
            
            if not preset_data:
                self.report({'ERROR'}, "Preset not found")
                return {'CANCELLED'}
            
            # Apply to active object temporarily
            obj = context.active_object
            
            # Store original animation data
            original_action = obj.animation_data.action if obj.animation_data else None
            
            # Apply preview
            success = RigaddonAnimationCore.rigaddon_apply_preset_to_object(
                obj, preset_data, context.scene.frame_current
            )
            
            if success:
                self.report({'INFO'}, f"Previewing preset '{preset_data.get('name', 'Unknown')}'")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to preview preset")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error previewing preset: {str(e)}")
            return {'CANCELLED'}

class RIGADDON_OT_ClearPreview(Operator):
    """Clear animation preview"""
    bl_idname = "rigaddon.clear_preview"
    bl_label = "Clear Preview"
    bl_description = "Clear animation preview from object"
    bl_options = {'REGISTER', 'UNDO'}
    
    @classmethod
    def poll(cls, context):
        return context.active_object is not None
    
    def execute(self, context):
        try:
            obj = context.active_object
            
            if obj.animation_data:
                obj.animation_data_clear()
                self.report({'INFO'}, "Preview cleared")
            else:
                self.report({'INFO'}, "No animation data to clear")
            
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Error clearing preview: {str(e)}")
            return {'CANCELLED'}
