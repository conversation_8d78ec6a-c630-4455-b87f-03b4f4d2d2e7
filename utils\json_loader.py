# Rigaddon Animation - JSON Loader Utility
# Created by: <PERSON><PERSON><PERSON>

import json
import os
import bpy
from typing import Dict, List, Any, Optional, Union

class RigaddonJSONLoader:
    """Utility class for loading and saving JSON data for Rigaddon Animation"""
    
    @staticmethod
    def rigaddon_load_json_file(file_path: str) -> Optional[Dict[str, Any]]:
        """Load JSON data from file"""
        try:
            if not os.path.exists(file_path):
                print(f"Rigaddon Animation: JSON file not found - {file_path}")
                return None
            
            with open(file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)
            
            print(f"Rigaddon Animation: Successfully loaded JSON from {file_path}")
            return data
            
        except json.JSONDecodeError as e:
            print(f"Rigaddon Animation: JSON decode error - {e}")
            return None
        except Exception as e:
            print(f"Rigaddon Animation: Error loading JSON file - {e}")
            return None
    
    @staticmethod
    def rigaddon_save_json_file(data: Dict[str, Any], file_path: str, indent: int = 2) -> bool:
        """Save data to JSON file"""
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as file:
                json.dump(data, file, indent=indent, ensure_ascii=False)
            
            print(f"Rigaddon Animation: Successfully saved JSON to {file_path}")
            return True
            
        except Exception as e:
            print(f"Rigaddon Animation: Error saving JSON file - {e}")
            return False
    
    @staticmethod
    def rigaddon_validate_preset_data(preset_data: Dict[str, Any]) -> bool:
        """Validate preset data structure"""
        required_fields = ['name', 'duration', 'properties']
        
        try:
            # Check required fields
            for field in required_fields:
                if field not in preset_data:
                    print(f"Rigaddon Animation: Missing required field '{field}' in preset data")
                    return False
            
            # Validate properties structure
            properties = preset_data.get('properties', {})
            if not isinstance(properties, dict):
                print("Rigaddon Animation: Properties must be a dictionary")
                return False
            
            # Validate keyframes in properties
            for prop_name, prop_data in properties.items():
                if 'keyframes' not in prop_data:
                    print(f"Rigaddon Animation: Missing keyframes in property '{prop_name}'")
                    return False
                
                keyframes = prop_data['keyframes']
                if not isinstance(keyframes, list):
                    print(f"Rigaddon Animation: Keyframes must be a list in property '{prop_name}'")
                    return False
                
                # Validate individual keyframes
                for i, keyframe in enumerate(keyframes):
                    if not isinstance(keyframe, dict):
                        print(f"Rigaddon Animation: Keyframe {i} must be a dictionary in property '{prop_name}'")
                        return False
                    
                    if 'frame' not in keyframe or 'value' not in keyframe:
                        print(f"Rigaddon Animation: Keyframe {i} missing frame or value in property '{prop_name}'")
                        return False
            
            return True
            
        except Exception as e:
            print(f"Rigaddon Animation: Error validating preset data - {e}")
            return False
    
    @staticmethod
    def rigaddon_export_animation_to_json(obj: bpy.types.Object, start_frame: int, 
                                         end_frame: int, name: str, description: str = "") -> Dict[str, Any]:
        """Export object animation to JSON format"""
        try:
            from .animation_core import RigaddonAnimationCore
            
            # Extract keyframes
            keyframes_data = RigaddonAnimationCore.rigaddon_get_object_keyframes(obj, start_frame, end_frame)
            
            # Build preset structure
            preset_data = {
                "name": name,
                "description": description,
                "duration": end_frame - start_frame + 1,
                "properties": {},
                "easing": "ease_in_out",
                "loop": False
            }
            
            # Convert keyframes to preset format
            for prop_name, prop_keyframes in keyframes_data.items():
                if prop_keyframes:  # Only include properties that have keyframes
                    if prop_name in ['location', 'rotation_euler', 'scale']:
                        # Handle vector properties
                        if len(prop_keyframes) >= 3:  # X, Y, Z components
                            combined_keyframes = []
                            
                            # Get all unique frames
                            all_frames = set()
                            for component_keyframes in prop_keyframes:
                                for kf in component_keyframes:
                                    all_frames.add(kf['frame'])
                            
                            # Build combined keyframes
                            for frame in sorted(all_frames):
                                value = [0.0, 0.0, 0.0]
                                interpolation = 'BEZIER'
                                
                                for i, component_keyframes in enumerate(prop_keyframes[:3]):
                                    for kf in component_keyframes:
                                        if kf['frame'] == frame:
                                            value[i] = kf['value']
                                            interpolation = kf['interpolation']
                                            break
                                
                                combined_keyframes.append({
                                    "frame": frame,
                                    "value": value,
                                    "interpolation": interpolation
                                })
                            
                            if combined_keyframes:
                                preset_data["properties"][prop_name] = {
                                    "keyframes": combined_keyframes
                                }
                    
                    elif prop_name == 'alpha':
                        # Handle scalar properties
                        if prop_keyframes:
                            preset_data["properties"][prop_name] = {
                                "keyframes": prop_keyframes
                            }
            
            return preset_data
            
        except Exception as e:
            print(f"Rigaddon Animation: Error exporting animation to JSON - {e}")
            return {}
    
    @staticmethod
    def rigaddon_backup_presets(backup_path: str) -> bool:
        """Create backup of current presets"""
        try:
            from ..presets.utils_presets import rigaddon_preset_manager
            
            preset_data = {
                "animation_presets": rigaddon_preset_manager.get_rigaddon_animation_presets(),
                "easing_curves": rigaddon_preset_manager.get_rigaddon_easing_curves(),
                "default_settings": rigaddon_preset_manager.get_rigaddon_default_settings(),
                "backup_info": {
                    "created_by": "Rigaddon Animation",
                    "blender_version": bpy.app.version_string,
                    "addon_version": "1.0.0"
                }
            }
            
            return RigaddonJSONLoader.rigaddon_save_json_file(preset_data, backup_path)
            
        except Exception as e:
            print(f"Rigaddon Animation: Error creating backup - {e}")
            return False
    
    @staticmethod
    def rigaddon_restore_presets(backup_path: str) -> bool:
        """Restore presets from backup"""
        try:
            backup_data = RigaddonJSONLoader.rigaddon_load_json_file(backup_path)
            if not backup_data:
                return False
            
            from ..presets.utils_presets import rigaddon_preset_manager
            
            # Validate backup data
            if "animation_presets" not in backup_data:
                print("Rigaddon Animation: Invalid backup file - missing animation_presets")
                return False
            
            # Restore data
            rigaddon_preset_manager._presets_data = {
                "animation_presets": backup_data.get("animation_presets", {}),
                "easing_curves": backup_data.get("easing_curves", {}),
                "default_settings": backup_data.get("default_settings", {})
            }
            
            # Save restored data
            return rigaddon_preset_manager.save_presets()
            
        except Exception as e:
            print(f"Rigaddon Animation: Error restoring backup - {e}")
            return False
