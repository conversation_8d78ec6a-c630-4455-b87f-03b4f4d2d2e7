# Rigaddon Animation - Preset Utilities
# Created by: <PERSON><PERSON><PERSON>

import json
import os
import bpy
from typing import Dict, List, Any, Optional

class RigaddonPresetManager:
    """Manager class for handling animation presets from JSON files"""
    
    _instance = None
    _presets_data = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(RigaddonPresetManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._presets_data is None:
            self.load_presets()
    
    @property
    def rigaddon_preset_file_path(self) -> str:
        """Get the path to the preset JSON file"""
        addon_dir = os.path.dirname(os.path.dirname(__file__))
        return os.path.join(addon_dir, "presets", "anim_presets.json")
    
    def load_presets(self) -> bool:
        """Load animation presets from JSON file"""
        try:
            preset_path = self.rigaddon_preset_file_path
            if not os.path.exists(preset_path):
                print(f"Rigaddon Animation: Preset file not found at {preset_path}")
                self._presets_data = {"animation_presets": {}, "easing_curves": {}, "default_settings": {}}
                return False
            
            with open(preset_path, 'r', encoding='utf-8') as file:
                self._presets_data = json.load(file)
            
            print("Rigaddon Animation: Presets loaded successfully")
            return True
            
        except Exception as e:
            print(f"Rigaddon Animation: Error loading presets - {e}")
            self._presets_data = {"animation_presets": {}, "easing_curves": {}, "default_settings": {}}
            return False
    
    def save_presets(self) -> bool:
        """Save current presets to JSON file"""
        try:
            preset_path = self.rigaddon_preset_file_path
            with open(preset_path, 'w', encoding='utf-8') as file:
                json.dump(self._presets_data, file, indent=2, ensure_ascii=False)
            
            print("Rigaddon Animation: Presets saved successfully")
            return True
            
        except Exception as e:
            print(f"Rigaddon Animation: Error saving presets - {e}")
            return False
    
    def get_rigaddon_animation_presets(self) -> Dict[str, Any]:
        """Get all animation presets"""
        if self._presets_data is None:
            self.load_presets()
        return self._presets_data.get("animation_presets", {})
    
    def get_rigaddon_preset_categories(self) -> List[str]:
        """Get list of preset categories"""
        presets = self.get_rigaddon_animation_presets()
        return list(presets.keys())
    
    def get_rigaddon_presets_in_category(self, category: str) -> Dict[str, Any]:
        """Get all presets in a specific category"""
        presets = self.get_rigaddon_animation_presets()
        return presets.get(category, {})
    
    def get_rigaddon_preset_by_name(self, category: str, preset_name: str) -> Optional[Dict[str, Any]]:
        """Get a specific preset by category and name"""
        category_presets = self.get_rigaddon_presets_in_category(category)
        return category_presets.get(preset_name)
    
    def get_rigaddon_easing_curves(self) -> Dict[str, Any]:
        """Get all easing curve definitions"""
        if self._presets_data is None:
            self.load_presets()
        return self._presets_data.get("easing_curves", {})
    
    def get_rigaddon_default_settings(self) -> Dict[str, Any]:
        """Get default animation settings"""
        if self._presets_data is None:
            self.load_presets()
        return self._presets_data.get("default_settings", {})
    
    def add_rigaddon_custom_preset(self, category: str, preset_name: str, preset_data: Dict[str, Any]) -> bool:
        """Add a new custom preset"""
        try:
            if self._presets_data is None:
                self.load_presets()
            
            if category not in self._presets_data["animation_presets"]:
                self._presets_data["animation_presets"][category] = {}
            
            self._presets_data["animation_presets"][category][preset_name] = preset_data
            return self.save_presets()
            
        except Exception as e:
            print(f"Rigaddon Animation: Error adding custom preset - {e}")
            return False
    
    def remove_rigaddon_custom_preset(self, category: str, preset_name: str) -> bool:
        """Remove a custom preset"""
        try:
            if self._presets_data is None:
                self.load_presets()
            
            if category in self._presets_data["animation_presets"]:
                if preset_name in self._presets_data["animation_presets"][category]:
                    del self._presets_data["animation_presets"][category][preset_name]
                    
                    # Remove category if empty
                    if not self._presets_data["animation_presets"][category]:
                        del self._presets_data["animation_presets"][category]
                    
                    return self.save_presets()
            
            return False
            
        except Exception as e:
            print(f"Rigaddon Animation: Error removing custom preset - {e}")
            return False
    
    def get_rigaddon_preset_list_for_ui(self) -> List[tuple]:
        """Get formatted preset list for Blender UI"""
        preset_list = []
        presets = self.get_rigaddon_animation_presets()
        
        for category, category_presets in presets.items():
            for preset_name, preset_data in category_presets.items():
                identifier = f"{category}.{preset_name}"
                name = preset_data.get("name", preset_name)
                description = preset_data.get("description", "")
                preset_list.append((identifier, name, description))
        
        return preset_list
    
    def reload_rigaddon_presets(self) -> bool:
        """Reload presets from file"""
        self._presets_data = None
        return self.load_presets()

# Global instance
rigaddon_preset_manager = RigaddonPresetManager()
