# Rigaddon Animation - Playback Manager
# Created by: <PERSON><PERSON><PERSON>

import bpy
from typing import Dict, List, Any, Optional, Tuple
import time

class RigaddonPlaybackManager:
    """Manager for advanced animation playback controls"""
    
    _instance = None
    _playback_state = {
        'is_playing': False,
        'current_animation': None,
        'loop_mode': 'NONE',  # NONE, SINGLE, ALL
        'play_mode': 'NORMAL',  # NORMAL, REVERSE, PING_PONG
        'start_frame': 1,
        'end_frame': 250,
        'original_frame': 1
    }
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(RigaddonPlaybackManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        self.rigaddon_modal_timer = None
        self.rigaddon_ping_pong_direction = 1  # 1 for forward, -1 for reverse
    
    def rigaddon_play_animation(self, animation_item, loop: bool = False, 
                               reverse: bool = False) -> bool:
        """Play a specific animation"""
        try:
            if not animation_item:
                return False
            
            # Get target object
            target_obj = bpy.data.objects.get(animation_item.rigaddon_anim_target_object)
            if not target_obj:
                print(f"Rigaddon Animation: Target object '{animation_item.rigaddon_anim_target_object}' not found")
                return False
            
            # Set playback state
            self._playback_state['current_animation'] = animation_item
            self._playback_state['is_playing'] = True
            self._playback_state['loop_mode'] = 'SINGLE' if loop else 'NONE'
            self._playback_state['play_mode'] = 'REVERSE' if reverse else 'NORMAL'
            self._playback_state['start_frame'] = animation_item.rigaddon_anim_start_frame
            self._playback_state['end_frame'] = animation_item.rigaddon_anim_end_frame
            self._playback_state['original_frame'] = bpy.context.scene.frame_current
            
            # Set frame range
            bpy.context.scene.frame_start = animation_item.rigaddon_anim_start_frame
            bpy.context.scene.frame_end = animation_item.rigaddon_anim_end_frame
            
            # Set current frame
            if reverse:
                bpy.context.scene.frame_set(animation_item.rigaddon_anim_end_frame)
            else:
                bpy.context.scene.frame_set(animation_item.rigaddon_anim_start_frame)
            
            # Start playback
            bpy.ops.screen.animation_play()
            
            return True
            
        except Exception as e:
            print(f"Rigaddon Animation: Error playing animation - {e}")
            return False
    
    def rigaddon_stop_animation(self) -> bool:
        """Stop current animation playback"""
        try:
            # Stop Blender playback
            if bpy.context.screen.is_animation_playing:
                bpy.ops.screen.animation_cancel()
            
            # Reset playback state
            self._playback_state['is_playing'] = False
            self._playback_state['current_animation'] = None
            self._playback_state['loop_mode'] = 'NONE'
            
            # Restore original frame
            if self._playback_state['original_frame']:
                bpy.context.scene.frame_set(self._playback_state['original_frame'])
            
            return True
            
        except Exception as e:
            print(f"Rigaddon Animation: Error stopping animation - {e}")
            return False
    
    def rigaddon_play_from_animation(self, animation_item, loop: bool = False) -> bool:
        """Play from a specific animation to the end of timeline"""
        try:
            if not animation_item:
                return False
            
            # Set playback state
            self._playback_state['current_animation'] = animation_item
            self._playback_state['is_playing'] = True
            self._playback_state['loop_mode'] = 'ALL' if loop else 'NONE'
            self._playback_state['start_frame'] = animation_item.rigaddon_anim_start_frame
            self._playback_state['end_frame'] = bpy.context.scene.frame_end
            self._playback_state['original_frame'] = bpy.context.scene.frame_current
            
            # Set frame range
            bpy.context.scene.frame_start = animation_item.rigaddon_anim_start_frame
            
            # Set current frame to animation start
            bpy.context.scene.frame_set(animation_item.rigaddon_anim_start_frame)
            
            # Start playback
            bpy.ops.screen.animation_play()
            
            return True
            
        except Exception as e:
            print(f"Rigaddon Animation: Error playing from animation - {e}")
            return False
    
    def rigaddon_play_sequence(self, animation_list: List, loop: bool = False) -> bool:
        """Play multiple animations in sequence"""
        try:
            if not animation_list:
                return False
            
            # Calculate total frame range
            min_start = min(anim.rigaddon_anim_start_frame for anim in animation_list)
            max_end = max(anim.rigaddon_anim_end_frame for anim in animation_list)
            
            # Set playback state
            self._playback_state['is_playing'] = True
            self._playback_state['loop_mode'] = 'ALL' if loop else 'NONE'
            self._playback_state['start_frame'] = min_start
            self._playback_state['end_frame'] = max_end
            self._playback_state['original_frame'] = bpy.context.scene.frame_current
            
            # Set frame range
            bpy.context.scene.frame_start = min_start
            bpy.context.scene.frame_end = max_end
            
            # Set current frame
            bpy.context.scene.frame_set(min_start)
            
            # Start playback
            bpy.ops.screen.animation_play()
            
            return True
            
        except Exception as e:
            print(f"Rigaddon Animation: Error playing sequence - {e}")
            return False
    
    def rigaddon_toggle_pause(self) -> bool:
        """Toggle pause/resume playback"""
        try:
            if bpy.context.screen.is_animation_playing:
                bpy.ops.screen.animation_cancel()
            else:
                bpy.ops.screen.animation_play()
            
            return True
            
        except Exception as e:
            print(f"Rigaddon Animation: Error toggling pause - {e}")
            return False
    
    def rigaddon_step_forward(self, steps: int = 1) -> bool:
        """Step forward by specified number of frames"""
        try:
            current_frame = bpy.context.scene.frame_current
            new_frame = min(current_frame + steps, bpy.context.scene.frame_end)
            bpy.context.scene.frame_set(new_frame)
            return True
            
        except Exception as e:
            print(f"Rigaddon Animation: Error stepping forward - {e}")
            return False
    
    def rigaddon_step_backward(self, steps: int = 1) -> bool:
        """Step backward by specified number of frames"""
        try:
            current_frame = bpy.context.scene.frame_current
            new_frame = max(current_frame - steps, bpy.context.scene.frame_start)
            bpy.context.scene.frame_set(new_frame)
            return True
            
        except Exception as e:
            print(f"Rigaddon Animation: Error stepping backward - {e}")
            return False
    
    def rigaddon_jump_to_frame(self, frame: int) -> bool:
        """Jump to specific frame"""
        try:
            frame = max(bpy.context.scene.frame_start, 
                       min(frame, bpy.context.scene.frame_end))
            bpy.context.scene.frame_set(frame)
            return True
            
        except Exception as e:
            print(f"Rigaddon Animation: Error jumping to frame - {e}")
            return False
    
    def rigaddon_set_playback_speed(self, speed_factor: float) -> bool:
        """Set playback speed (not directly supported in Blender, but can modify frame rate)"""
        try:
            # This is a workaround - we can't directly change playback speed in Blender
            # But we can modify the frame rate for the effect
            original_fps = bpy.context.scene.render.fps
            new_fps = max(1, int(original_fps * speed_factor))
            bpy.context.scene.render.fps = new_fps
            
            print(f"Rigaddon Animation: Playback speed changed to {speed_factor}x (FPS: {new_fps})")
            return True
            
        except Exception as e:
            print(f"Rigaddon Animation: Error setting playback speed - {e}")
            return False
    
    def rigaddon_get_playback_info(self) -> Dict[str, Any]:
        """Get current playback information"""
        return {
            'is_playing': bpy.context.screen.is_animation_playing,
            'current_frame': bpy.context.scene.frame_current,
            'start_frame': bpy.context.scene.frame_start,
            'end_frame': bpy.context.scene.frame_end,
            'fps': bpy.context.scene.render.fps,
            'rigaddon_state': self._playback_state.copy()
        }
    
    def rigaddon_reset_playback(self) -> bool:
        """Reset playback to default state"""
        try:
            self.rigaddon_stop_animation()
            
            # Reset frame range to scene defaults
            bpy.context.scene.frame_start = 1
            bpy.context.scene.frame_end = 250
            bpy.context.scene.frame_set(1)
            
            # Reset playback state
            self._playback_state = {
                'is_playing': False,
                'current_animation': None,
                'loop_mode': 'NONE',
                'play_mode': 'NORMAL',
                'start_frame': 1,
                'end_frame': 250,
                'original_frame': 1
            }
            
            return True
            
        except Exception as e:
            print(f"Rigaddon Animation: Error resetting playback - {e}")
            return False

# Global instance
rigaddon_playback_manager = RigaddonPlaybackManager()
