# Rigaddon Animation - Play Animation Operators
# Created by: <PERSON><PERSON><PERSON>

import bpy
from bpy.types import Operator
from bpy.props import String<PERSON>roperty, IntProperty, BoolProperty, EnumProperty

class RIGADDON_OT_PlayAnimation(Operator):
    """Play selected animation"""
    bl_idname = "rigaddon.play_animation"
    bl_label = "Play Animation"
    bl_description = "Play the selected animation"
    bl_options = {'REGISTER'}
    
    rigaddon_animation_index: IntProperty(
        name="Animation Index",
        description="Index of animation to play",
        default=-1
    )
    
    rigaddon_loop: BoolProperty(
        name="Loop",
        description="Loop the animation",
        default=False
    )
    
    rigaddon_reverse: BoolProperty(
        name="Reverse",
        description="Play animation in reverse",
        default=False
    )
    
    @classmethod
    def poll(cls, context):
        anim_props = context.scene.rigaddon_animation_props
        return len(anim_props.rigaddon_animation_list) > 0
    
    def execute(self, context):
        try:
            from ..utils.playback import rigaddon_playback_manager
            
            anim_props = context.scene.rigaddon_animation_props
            
            # Get animation index
            if self.rigaddon_animation_index >= 0:
                anim_index = self.rigaddon_animation_index
            else:
                anim_index = anim_props.rigaddon_animation_list_index
            
            # Validate index
            if anim_index >= len(anim_props.rigaddon_animation_list):
                self.report({'ERROR'}, "Invalid animation index")
                return {'CANCELLED'}
            
            animation_item = anim_props.rigaddon_animation_list[anim_index]
            
            # Check if animation is enabled
            if not animation_item.rigaddon_anim_enabled:
                self.report({'WARNING'}, f"Animation '{animation_item.rigaddon_anim_name}' is disabled")
                return {'CANCELLED'}
            
            # Play animation
            success = rigaddon_playback_manager.rigaddon_play_animation(
                animation_item, self.rigaddon_loop, self.rigaddon_reverse
            )
            
            if success:
                mode_text = "reverse" if self.rigaddon_reverse else "forward"
                loop_text = " (looping)" if self.rigaddon_loop else ""
                self.report({'INFO'}, f"Playing '{animation_item.rigaddon_anim_name}' {mode_text}{loop_text}")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to play animation")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error playing animation: {str(e)}")
            return {'CANCELLED'}

class RIGADDON_OT_PlayAnimationLoop(Operator):
    """Play selected animation in loop"""
    bl_idname = "rigaddon.play_animation_loop"
    bl_label = "Play Animation Loop"
    bl_description = "Play the selected animation in loop"
    bl_options = {'REGISTER'}
    
    rigaddon_animation_index: IntProperty(
        name="Animation Index",
        description="Index of animation to play",
        default=-1
    )
    
    @classmethod
    def poll(cls, context):
        anim_props = context.scene.rigaddon_animation_props
        return len(anim_props.rigaddon_animation_list) > 0
    
    def execute(self, context):
        # Use the main play operator with loop enabled
        bpy.ops.rigaddon.play_animation(
            rigaddon_animation_index=self.rigaddon_animation_index,
            rigaddon_loop=True
        )
        return {'FINISHED'}

class RIGADDON_OT_PlayFromAnimation(Operator):
    """Play from selected animation to end"""
    bl_idname = "rigaddon.play_from_animation"
    bl_label = "Play From Animation"
    bl_description = "Play from the selected animation to the end of timeline"
    bl_options = {'REGISTER'}
    
    rigaddon_animation_index: IntProperty(
        name="Animation Index",
        description="Index of animation to start from",
        default=-1
    )
    
    rigaddon_loop: BoolProperty(
        name="Loop",
        description="Loop from this animation",
        default=False
    )
    
    @classmethod
    def poll(cls, context):
        anim_props = context.scene.rigaddon_animation_props
        return len(anim_props.rigaddon_animation_list) > 0
    
    def execute(self, context):
        try:
            from ..utils.playback import rigaddon_playback_manager
            
            anim_props = context.scene.rigaddon_animation_props
            
            # Get animation index
            if self.rigaddon_animation_index >= 0:
                anim_index = self.rigaddon_animation_index
            else:
                anim_index = anim_props.rigaddon_animation_list_index
            
            # Validate index
            if anim_index >= len(anim_props.rigaddon_animation_list):
                self.report({'ERROR'}, "Invalid animation index")
                return {'CANCELLED'}
            
            animation_item = anim_props.rigaddon_animation_list[anim_index]
            
            # Check if animation is enabled
            if not animation_item.rigaddon_anim_enabled:
                self.report({'WARNING'}, f"Animation '{animation_item.rigaddon_anim_name}' is disabled")
                return {'CANCELLED'}
            
            # Play from animation
            success = rigaddon_playback_manager.rigaddon_play_from_animation(
                animation_item, self.rigaddon_loop
            )
            
            if success:
                loop_text = " (looping)" if self.rigaddon_loop else ""
                self.report({'INFO'}, f"Playing from '{animation_item.rigaddon_anim_name}'{loop_text}")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to play from animation")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error playing from animation: {str(e)}")
            return {'CANCELLED'}

class RIGADDON_OT_StopAnimation(Operator):
    """Stop animation playback"""
    bl_idname = "rigaddon.stop_animation"
    bl_label = "Stop Animation"
    bl_description = "Stop current animation playback"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            from ..utils.playback import rigaddon_playback_manager
            
            success = rigaddon_playback_manager.rigaddon_stop_animation()
            
            if success:
                self.report({'INFO'}, "Animation playback stopped")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to stop animation")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error stopping animation: {str(e)}")
            return {'CANCELLED'}

class RIGADDON_OT_TogglePause(Operator):
    """Toggle pause/resume animation"""
    bl_idname = "rigaddon.toggle_pause"
    bl_label = "Toggle Pause"
    bl_description = "Toggle pause/resume animation playback"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            from ..utils.playback import rigaddon_playback_manager
            
            success = rigaddon_playback_manager.rigaddon_toggle_pause()
            
            if success:
                is_playing = context.screen.is_animation_playing
                status = "resumed" if is_playing else "paused"
                self.report({'INFO'}, f"Animation {status}")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to toggle pause")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error toggling pause: {str(e)}")
            return {'CANCELLED'}

class RIGADDON_OT_PlayAllAnimations(Operator):
    """Play all animations in sequence"""
    bl_idname = "rigaddon.play_all_animations"
    bl_label = "Play All Animations"
    bl_description = "Play all enabled animations in sequence"
    bl_options = {'REGISTER'}
    
    rigaddon_loop: BoolProperty(
        name="Loop",
        description="Loop all animations",
        default=False
    )
    
    @classmethod
    def poll(cls, context):
        anim_props = context.scene.rigaddon_animation_props
        return len(anim_props.rigaddon_animation_list) > 0
    
    def execute(self, context):
        try:
            from ..utils.playback import rigaddon_playback_manager
            
            anim_props = context.scene.rigaddon_animation_props
            
            # Get enabled animations
            enabled_animations = [
                anim for anim in anim_props.rigaddon_animation_list 
                if anim.rigaddon_anim_enabled
            ]
            
            if not enabled_animations:
                self.report({'WARNING'}, "No enabled animations found")
                return {'CANCELLED'}
            
            # Play sequence
            success = rigaddon_playback_manager.rigaddon_play_sequence(
                enabled_animations, self.rigaddon_loop
            )
            
            if success:
                loop_text = " (looping)" if self.rigaddon_loop else ""
                self.report({'INFO'}, f"Playing {len(enabled_animations)} animations{loop_text}")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to play animation sequence")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error playing all animations: {str(e)}")
            return {'CANCELLED'}
