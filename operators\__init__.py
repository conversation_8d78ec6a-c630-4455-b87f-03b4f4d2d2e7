# Rigaddon Animation - Operators Module
# Created by: <PERSON><PERSON><PERSON>

from .add_animation import RIGADDON_OT_AddAnimation
from .play_animation import (
    RIGADDON_OT_PlayAnimation,
    RIGADDON_OT_PlayAnimationLoop,
    RIGADDON_OT_PlayFromAnimation,
    RIGADDON_OT_StopAnimation,
    RIGADDON_OT_TogglePause,
    RIGADDON_OT_PlayAllAnimations,
)
from .remove_animation import (
    RIGADDON_OT_RemoveAnimation,
    RIGADDON_OT_ClearAllAnimations,
    RIGADDON_OT_DuplicateAnimation,
)
from .custom_controls import (
    RIGADDON_OT_CustomizeAnimation,
    RIGADDON_OT_UpdateAnimationProperty,
    RIGADDON_OT_ScaleAnimation,
    RIGADDON_OT_ReverseAnimation,
    RIGADDON_OT_ToggleAnimationEnabled,
)
from .preset_operators import (
    RIGADDON_OT_CreateCustomPreset,
    RIGADDON_OT_ReloadPresets,
    RIGADDON_OT_BackupPresets,
    RIG<PERSON>DON_OT_RestorePresets,
    RIGADDON_OT_DeleteCustomPreset,
    R<PERSON><PERSON>DON_OT_PreviewPreset,
    RIGADDON_OT_ClearPreview,
)
from .advanced_operators import (
    RIGADDON_OT_BlendAnimations,
    RIGADDON_OT_CreatePathAnimation,
    RIGADDON_OT_CreateNoiseAnimation,
    RIGADDON_OT_CreatePhysicsAnimation,
    RIGADDON_OT_BatchApplyAnimation,
)

rigaddon_operator_classes = [
    RIGADDON_OT_AddAnimation,
    RIGADDON_OT_PlayAnimation,
    RIGADDON_OT_PlayAnimationLoop,
    RIGADDON_OT_PlayFromAnimation,
    RIGADDON_OT_StopAnimation,
    RIGADDON_OT_TogglePause,
    RIGADDON_OT_PlayAllAnimations,
    RIGADDON_OT_RemoveAnimation,
    RIGADDON_OT_ClearAllAnimations,
    RIGADDON_OT_DuplicateAnimation,
    RIGADDON_OT_CustomizeAnimation,
    RIGADDON_OT_UpdateAnimationProperty,
    RIGADDON_OT_ScaleAnimation,
    RIGADDON_OT_ReverseAnimation,
    RIGADDON_OT_ToggleAnimationEnabled,
    RIGADDON_OT_CreateCustomPreset,
    RIGADDON_OT_ReloadPresets,
    RIGADDON_OT_BackupPresets,
    RIGADDON_OT_RestorePresets,
    RIGADDON_OT_DeleteCustomPreset,
    RIGADDON_OT_PreviewPreset,
    RIGADDON_OT_ClearPreview,
    RIGADDON_OT_BlendAnimations,
    RIGADDON_OT_CreatePathAnimation,
    RIGADDON_OT_CreateNoiseAnimation,
    RIGADDON_OT_CreatePhysicsAnimation,
    RIGADDON_OT_BatchApplyAnimation,
]

def register():
    """Register operator classes"""
    from bpy.utils import register_class
    for cls in rigaddon_operator_classes:
        register_class(cls)

def unregister():
    """Unregister operator classes"""
    from bpy.utils import unregister_class
    for cls in reversed(rigaddon_operator_classes):
        unregister_class(cls)
