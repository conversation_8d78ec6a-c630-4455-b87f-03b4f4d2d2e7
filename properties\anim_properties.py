# Rigaddon Animation - Animation Properties
# Created by: <PERSON><PERSON><PERSON>

import bpy
from bpy.props import (
    StringProperty, IntProperty, FloatProperty, BoolProperty,
    EnumProperty, CollectionProperty, PointerProperty, FloatVectorProperty
)
from bpy.types import PropertyGroup

class RigaddonAnimationKeyframe(PropertyGroup):
    """Individual keyframe data for Rigaddon animations"""
    
    rigaddon_frame: IntProperty(
        name="Frame",
        description="Frame number for this keyframe",
        default=1,
        min=1
    )
    
    rigaddon_location_value: FloatVectorProperty(
        name="Location",
        description="Location value for this keyframe",
        size=3,
        default=(0.0, 0.0, 0.0)
    )
    
    rigaddon_rotation_value: FloatVectorProperty(
        name="Rotation",
        description="Rotation value for this keyframe (Euler)",
        size=3,
        default=(0.0, 0.0, 0.0)
    )
    
    rigaddon_scale_value: FloatVectorProperty(
        name="Scale",
        description="Scale value for this keyframe",
        size=3,
        default=(1.0, 1.0, 1.0)
    )
    
    rigaddon_alpha_value: <PERSON>loat<PERSON>roper<PERSON>(
        name="Alpha",
        description="Alpha/transparency value for this keyframe",
        default=1.0,
        min=0.0,
        max=1.0
    )
    
    rigaddon_interpolation: EnumProperty(
        name="Interpolation",
        description="Interpolation type for this keyframe",
        items=[
            ('BEZIER', 'Bezier', 'Smooth bezier interpolation'),
            ('LINEAR', 'Linear', 'Linear interpolation'),
            ('CONSTANT', 'Constant', 'No interpolation'),
        ],
        default='BEZIER'
    )

class RigaddonAnimationItem(PropertyGroup):
    """Individual animation item in the list"""
    
    rigaddon_anim_name: StringProperty(
        name="Animation Name",
        description="Name of this animation",
        default="New Animation"
    )
    
    rigaddon_anim_description: StringProperty(
        name="Description",
        description="Description of this animation",
        default=""
    )
    
    rigaddon_anim_duration: IntProperty(
        name="Duration",
        description="Duration of animation in frames",
        default=60,
        min=1
    )
    
    rigaddon_anim_start_frame: IntProperty(
        name="Start Frame",
        description="Starting frame for this animation",
        default=1,
        min=1
    )
    
    rigaddon_anim_end_frame: IntProperty(
        name="End Frame",
        description="Ending frame for this animation",
        default=60,
        min=1
    )
    
    rigaddon_anim_loop: BoolProperty(
        name="Loop",
        description="Whether this animation should loop",
        default=False
    )
    
    rigaddon_anim_enabled: BoolProperty(
        name="Enabled",
        description="Whether this animation is enabled",
        default=True
    )
    
    rigaddon_anim_target_object: StringProperty(
        name="Target Object",
        description="Name of the target object for this animation",
        default=""
    )
    
    rigaddon_anim_preset_category: StringProperty(
        name="Preset Category",
        description="Category of the preset used",
        default=""
    )
    
    rigaddon_anim_preset_name: StringProperty(
        name="Preset Name",
        description="Name of the preset used",
        default=""
    )
    
    rigaddon_anim_easing: EnumProperty(
        name="Easing",
        description="Easing type for this animation",
        items=[
            ('linear', 'Linear', 'Linear easing'),
            ('ease_in', 'Ease In', 'Ease in'),
            ('ease_out', 'Ease Out', 'Ease out'),
            ('ease_in_out', 'Ease In Out', 'Ease in and out'),
            ('bounce', 'Bounce', 'Bounce easing'),
            ('elastic', 'Elastic', 'Elastic easing'),
        ],
        default='ease_in_out'
    )
    
    rigaddon_anim_keyframes: CollectionProperty(
        type=RigaddonAnimationKeyframe,
        name="Keyframes"
    )
    
    # Custom properties for advanced control
    rigaddon_anim_use_location: BoolProperty(
        name="Animate Location",
        description="Enable location animation",
        default=True
    )
    
    rigaddon_anim_use_rotation: BoolProperty(
        name="Animate Rotation",
        description="Enable rotation animation",
        default=False
    )
    
    rigaddon_anim_use_scale: BoolProperty(
        name="Animate Scale",
        description="Enable scale animation",
        default=False
    )
    
    rigaddon_anim_use_alpha: BoolProperty(
        name="Animate Alpha",
        description="Enable alpha/transparency animation",
        default=False
    )
    
    rigaddon_anim_speed_multiplier: FloatProperty(
        name="Speed Multiplier",
        description="Speed multiplier for this animation",
        default=1.0,
        min=0.1,
        max=10.0
    )

class RigaddonAnimationProperties(PropertyGroup):
    """Main properties for Rigaddon Animation system"""
    
    rigaddon_animation_list: CollectionProperty(
        type=RigaddonAnimationItem,
        name="Animation List"
    )
    
    rigaddon_animation_list_index: IntProperty(
        name="Animation List Index",
        description="Index of selected animation in list",
        default=0
    )
    
    rigaddon_show_advanced: BoolProperty(
        name="Show Advanced",
        description="Show advanced animation controls",
        default=False
    )
    
    rigaddon_auto_preview: BoolProperty(
        name="Auto Preview",
        description="Automatically preview animations when selected",
        default=True
    )
    
    rigaddon_global_speed: FloatProperty(
        name="Global Speed",
        description="Global speed multiplier for all animations",
        default=1.0,
        min=0.1,
        max=5.0
    )
    
    rigaddon_playback_mode: EnumProperty(
        name="Playback Mode",
        description="Global playback mode",
        items=[
            ('SINGLE', 'Single', 'Play single animation'),
            ('SEQUENCE', 'Sequence', 'Play animations in sequence'),
            ('PARALLEL', 'Parallel', 'Play animations in parallel'),
        ],
        default='SINGLE'
    )
