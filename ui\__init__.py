# Rigaddon Animation - UI Module
# Created by: <PERSON><PERSON><PERSON>

from .panels import (
    RIGADDON_PT_AnimationMainPanel,
    RIGADDON_PT_AnimationControlPanel,
    RIGADDON_PT_AnimationPresetsPanel,
    RIGADDON_PT_AnimationToolsPanel,
    <PERSON><PERSON><PERSON><PERSON><PERSON>_PT_AdvancedFeaturesPanel,
    <PERSON><PERSON><PERSON><PERSON><PERSON>_PT_TestPanel,
)
from .lists import (
    RIGADDON_UL_AnimationList,
    RIGADDON_OT_AnimationListActions,
    RIGADDON_OT_SelectAnimationTarget,
    RIGADDON_OT_JumpToAnimationFrame,
)
from .icons import (
    rigaddon_load_icons,
    rigaddon_unload_icons,
    RigaddonUIHelpers,
)

rigaddon_ui_classes = [
    RIGADDON_PT_TestPanel,  # Put test panel first
    RIGADDON_PT_AnimationMainPanel,
    RIGADDON_PT_AnimationControlPanel,
    RIGADDON_PT_AnimationPresetsPanel,
    RIGADDON_PT_AnimationToolsPanel,
    RIGADD<PERSON>_PT_AdvancedFeaturesPanel,
    R<PERSON><PERSON><PERSON><PERSON>_UL_AnimationList,
    R<PERSON><PERSON><PERSON><PERSON>_OT_AnimationListActions,
    RIGADDON_OT_SelectAnimationTarget,
    R<PERSON><PERSON>DON_OT_JumpToAnimationFrame,
]

def register():
    """Register UI classes"""
    from bpy.utils import register_class
    for cls in rigaddon_ui_classes:
        register_class(cls)

    # Load icons
    rigaddon_load_icons()

def unregister():
    """Unregister UI classes"""
    # Unload icons
    rigaddon_unload_icons()

    from bpy.utils import unregister_class
    for cls in reversed(rigaddon_ui_classes):
        unregister_class(cls)
