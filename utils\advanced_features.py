# Rigaddon Animation - Advanced Features
# Created by: <PERSON><PERSON><PERSON> Tapangan

import bpy
import bmesh
import mathutils
from mathutils import Vector, Euler, Quaternion, Matrix
from typing import Dict, List, Any, Optional, Tuple
import math

class RigaddonAdvancedFeatures:
    """Advanced animation features for professional workflow"""
    
    @staticmethod
    def rigaddon_create_animation_curve(curve_type: str, start_value: float, end_value: float, 
                                       frames: int, offset: float = 0.0) -> List[float]:
        """Create custom animation curves"""
        values = []
        
        for i in range(frames):
            t = i / (frames - 1) if frames > 1 else 0
            
            if curve_type == 'linear':
                value = start_value + (end_value - start_value) * t
                
            elif curve_type == 'ease_in':
                t = t * t
                value = start_value + (end_value - start_value) * t
                
            elif curve_type == 'ease_out':
                t = 1 - (1 - t) * (1 - t)
                value = start_value + (end_value - start_value) * t
                
            elif curve_type == 'ease_in_out':
                if t < 0.5:
                    t = 2 * t * t
                else:
                    t = 1 - 2 * (1 - t) * (1 - t)
                value = start_value + (end_value - start_value) * t
                
            elif curve_type == 'bounce':
                if t < 1/2.75:
                    t = 7.5625 * t * t
                elif t < 2/2.75:
                    t = 7.5625 * (t - 1.5/2.75) * (t - 1.5/2.75) + 0.75
                elif t < 2.5/2.75:
                    t = 7.5625 * (t - 2.25/2.75) * (t - 2.25/2.75) + 0.9375
                else:
                    t = 7.5625 * (t - 2.625/2.75) * (t - 2.625/2.75) + 0.984375
                value = start_value + (end_value - start_value) * t
                
            elif curve_type == 'elastic':
                if t == 0:
                    t = 0
                elif t == 1:
                    t = 1
                else:
                    p = 0.3
                    s = p / 4
                    t = math.pow(2, -10 * t) * math.sin((t - s) * (2 * math.pi) / p) + 1
                value = start_value + (end_value - start_value) * t
                
            elif curve_type == 'sine_wave':
                value = start_value + (end_value - start_value) * (math.sin(t * math.pi * 2 + offset) + 1) / 2
                
            else:  # default to linear
                value = start_value + (end_value - start_value) * t
            
            values.append(value)
        
        return values
    
    @staticmethod
    def rigaddon_blend_animations(obj: bpy.types.Object, blend_factor: float, 
                                 animation1_start: int, animation1_end: int,
                                 animation2_start: int, animation2_end: int) -> bool:
        """Blend two animations together"""
        try:
            if not obj.animation_data or not obj.animation_data.action:
                return False
            
            action = obj.animation_data.action
            
            # Get keyframes from both animation ranges
            anim1_keyframes = {}
            anim2_keyframes = {}
            
            for fcurve in action.fcurves:
                data_path = fcurve.data_path
                array_index = fcurve.array_index
                
                if data_path not in anim1_keyframes:
                    anim1_keyframes[data_path] = {}
                    anim2_keyframes[data_path] = {}
                
                if array_index not in anim1_keyframes[data_path]:
                    anim1_keyframes[data_path][array_index] = []
                    anim2_keyframes[data_path][array_index] = []
                
                # Collect keyframes from both ranges
                for keyframe in fcurve.keyframe_points:
                    frame = int(keyframe.co[0])
                    value = keyframe.co[1]
                    
                    if animation1_start <= frame <= animation1_end:
                        anim1_keyframes[data_path][array_index].append((frame, value))
                    elif animation2_start <= frame <= animation2_end:
                        anim2_keyframes[data_path][array_index].append((frame, value))
            
            # Create blended animation
            blend_start = max(animation1_start, animation2_start)
            blend_end = min(animation1_end, animation2_end)
            
            for data_path in anim1_keyframes:
                for array_index in anim1_keyframes[data_path]:
                    # Find corresponding fcurve
                    target_fcurve = None
                    for fcurve in action.fcurves:
                        if fcurve.data_path == data_path and fcurve.array_index == array_index:
                            target_fcurve = fcurve
                            break
                    
                    if target_fcurve:
                        # Create blended keyframes
                        for frame in range(blend_start, blend_end + 1):
                            # Get values from both animations
                            value1 = RigaddonAdvancedFeatures.rigaddon_get_value_at_frame(
                                anim1_keyframes[data_path][array_index], frame
                            )
                            value2 = RigaddonAdvancedFeatures.rigaddon_get_value_at_frame(
                                anim2_keyframes[data_path][array_index], frame
                            )
                            
                            if value1 is not None and value2 is not None:
                                # Blend values
                                blended_value = value1 * (1 - blend_factor) + value2 * blend_factor
                                
                                # Add keyframe
                                target_fcurve.keyframe_points.insert(frame, blended_value)
            
            return True
            
        except Exception as e:
            print(f"Rigaddon Animation: Error blending animations - {e}")
            return False
    
    @staticmethod
    def rigaddon_get_value_at_frame(keyframes: List[Tuple[int, float]], frame: int) -> Optional[float]:
        """Get interpolated value at specific frame"""
        if not keyframes:
            return None
        
        # Sort keyframes by frame
        keyframes.sort(key=lambda x: x[0])
        
        # Find surrounding keyframes
        for i, (kf_frame, kf_value) in enumerate(keyframes):
            if kf_frame == frame:
                return kf_value
            elif kf_frame > frame:
                if i == 0:
                    return kf_value
                else:
                    # Interpolate between previous and current keyframe
                    prev_frame, prev_value = keyframes[i-1]
                    t = (frame - prev_frame) / (kf_frame - prev_frame)
                    return prev_value + (kf_value - prev_value) * t
        
        # Frame is after all keyframes
        return keyframes[-1][1]
    
    @staticmethod
    def rigaddon_create_path_animation(obj: bpy.types.Object, path_points: List[Vector], 
                                      duration: int, start_frame: int = 1) -> bool:
        """Create animation following a path"""
        try:
            if len(path_points) < 2:
                return False
            
            # Clear existing location animation
            if obj.animation_data:
                for fcurve in obj.animation_data.action.fcurves:
                    if 'location' in fcurve.data_path:
                        obj.animation_data.action.fcurves.remove(fcurve)
            
            # Calculate frames per segment
            segments = len(path_points) - 1
            frames_per_segment = duration // segments
            
            # Create keyframes for each path point
            for i, point in enumerate(path_points):
                frame = start_frame + i * frames_per_segment
                
                # Set location
                obj.location = point
                obj.keyframe_insert(data_path="location", frame=frame)
            
            # Smooth the path
            if obj.animation_data and obj.animation_data.action:
                for fcurve in obj.animation_data.action.fcurves:
                    if 'location' in fcurve.data_path:
                        for keyframe in fcurve.keyframe_points:
                            keyframe.interpolation = 'BEZIER'
                            keyframe.handle_left_type = 'AUTO'
                            keyframe.handle_right_type = 'AUTO'
            
            return True
            
        except Exception as e:
            print(f"Rigaddon Animation: Error creating path animation - {e}")
            return False
    
    @staticmethod
    def rigaddon_create_noise_animation(obj: bpy.types.Object, property_name: str, 
                                       intensity: float, frequency: float, 
                                       duration: int, start_frame: int = 1) -> bool:
        """Create noise-based animation"""
        try:
            import random
            
            # Set random seed for reproducible results
            random.seed(42)
            
            frames = range(start_frame, start_frame + duration)
            
            for frame in frames:
                # Generate noise value
                t = (frame - start_frame) / duration
                noise_value = (random.random() - 0.5) * 2 * intensity
                
                # Apply frequency
                noise_value *= math.sin(t * frequency * math.pi * 2)
                
                # Set current frame
                bpy.context.scene.frame_set(frame)
                
                # Apply noise to property
                if property_name == 'location':
                    current_loc = Vector(obj.location)
                    obj.location = current_loc + Vector((noise_value, noise_value, noise_value))
                    obj.keyframe_insert(data_path="location", frame=frame)
                    
                elif property_name == 'rotation':
                    current_rot = Euler(obj.rotation_euler)
                    obj.rotation_euler = current_rot + Euler((noise_value, noise_value, noise_value))
                    obj.keyframe_insert(data_path="rotation_euler", frame=frame)
                    
                elif property_name == 'scale':
                    scale_factor = 1 + noise_value * 0.1
                    obj.scale = Vector((scale_factor, scale_factor, scale_factor))
                    obj.keyframe_insert(data_path="scale", frame=frame)
            
            return True
            
        except Exception as e:
            print(f"Rigaddon Animation: Error creating noise animation - {e}")
            return False
    
    @staticmethod
    def rigaddon_create_physics_simulation(obj: bpy.types.Object, simulation_type: str, 
                                          duration: int, start_frame: int = 1) -> bool:
        """Create physics-based animation"""
        try:
            if simulation_type == 'gravity_fall':
                # Simple gravity simulation
                gravity = -9.81
                initial_velocity = Vector((0, 0, 0))
                initial_position = Vector(obj.location)
                
                for frame in range(start_frame, start_frame + duration):
                    t = (frame - start_frame) / 24.0  # Convert to seconds (24 fps)
                    
                    # Calculate position with gravity
                    position = initial_position + initial_velocity * t + Vector((0, 0, 0.5 * gravity * t * t))
                    
                    # Set object position
                    bpy.context.scene.frame_set(frame)
                    obj.location = position
                    obj.keyframe_insert(data_path="location", frame=frame)
                
            elif simulation_type == 'pendulum':
                # Simple pendulum simulation
                length = 2.0
                angle_amplitude = math.pi / 4  # 45 degrees
                frequency = 1.0
                
                pivot_point = Vector(obj.location) + Vector((0, 0, length))
                
                for frame in range(start_frame, start_frame + duration):
                    t = (frame - start_frame) / 24.0
                    
                    # Calculate pendulum angle
                    angle = angle_amplitude * math.cos(frequency * t * 2 * math.pi)
                    
                    # Calculate position
                    x = pivot_point.x + length * math.sin(angle)
                    z = pivot_point.z - length * math.cos(angle)
                    position = Vector((x, pivot_point.y, z))
                    
                    # Set object position
                    bpy.context.scene.frame_set(frame)
                    obj.location = position
                    obj.keyframe_insert(data_path="location", frame=frame)
            
            return True
            
        except Exception as e:
            print(f"Rigaddon Animation: Error creating physics simulation - {e}")
            return False
    
    @staticmethod
    def rigaddon_batch_apply_animation(objects: List[bpy.types.Object], preset_data: Dict[str, Any], 
                                      offset_type: str = 'time', offset_value: float = 5.0) -> int:
        """Apply animation to multiple objects with various offset options"""
        try:
            from .animation_core import RigaddonAnimationCore
            
            applied_count = 0
            
            for i, obj in enumerate(objects):
                if offset_type == 'time':
                    # Time offset
                    start_frame = 1 + int(i * offset_value)
                    
                elif offset_type == 'random':
                    # Random offset
                    import random
                    start_frame = 1 + random.randint(0, int(offset_value))
                    
                elif offset_type == 'wave':
                    # Wave pattern offset
                    wave_offset = math.sin(i * 0.5) * offset_value
                    start_frame = 1 + int(wave_offset)
                    
                else:  # no offset
                    start_frame = 1
                
                # Apply animation
                success = RigaddonAnimationCore.rigaddon_apply_preset_to_object(
                    obj, preset_data, start_frame
                )
                
                if success:
                    applied_count += 1
            
            return applied_count
            
        except Exception as e:
            print(f"Rigaddon Animation: Error in batch apply - {e}")
            return 0
